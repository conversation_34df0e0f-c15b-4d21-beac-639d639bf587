"use client";

import { useCallback } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";

/**
 * Hook to refresh user and organization data
 * Useful for social login users to get the latest database information
 */
export function useDataRefresh() {
  const { refreshUserData } = useAuth();
  const { refreshOrganizations } = useOrganization();

  /**
   * Refresh both user data and organization data
   * This ensures social login users get the latest information from the database
   */
  const refreshAllData = useCallback(async () => {
    try {
      console.log("Refreshing all user and organization data...");
      
      // Refresh user data first (includes organizations)
      await refreshUserData();
      
      // Then refresh organization-specific data
      await refreshOrganizations();
      
      console.log("Data refresh completed successfully");
    } catch (error) {
      console.error("Failed to refresh data:", error);
    }
  }, [refreshUserData, refreshOrganizations]);

  /**
   * Refresh only user data
   */
  const refreshUser = useCallback(async () => {
    try {
      console.log("Refreshing user data...");
      await refreshUserData();
      console.log("User data refresh completed");
    } catch (error) {
      console.error("Failed to refresh user data:", error);
    }
  }, [refreshUserData]);

  /**
   * Refresh only organization data
   */
  const refreshOrgs = useCallback(async () => {
    try {
      console.log("Refreshing organization data...");
      await refreshOrganizations();
      console.log("Organization data refresh completed");
    } catch (error) {
      console.error("Failed to refresh organization data:", error);
    }
  }, [refreshOrganizations]);

  return {
    refreshAllData,
    refreshUser,
    refreshOrgs,
  };
}
