"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";

export function useOrganizationCheck() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const [showModal, setShowModal] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Only check once the user is authenticated and not loading
    if (isAuthenticated && !isLoading && user && !hasChecked) {
      // Check if user has any organizations
      const hasOrganizations =
        user.organizations && user.organizations.length > 0;

      console.log("📊 Organization check result:", {
        hasOrganizations,
        shouldShowModal: !hasOrganizations,
      });

      if (!hasOrganizations) {
        setShowModal(true);
      }

      setHasChecked(true);
    }
  }, [isAuthenticated, isLoading, user, hasChecked]);

  const handleOrganizationCreated = async () => {
    console.log("🎉 Organization created - refreshing user data");

    // Close the modal first to provide immediate feedback
    setShowModal(false);

    try {
      // Refresh user data from backend to get updated organizations
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/api/auth/me`,
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("auth_token")}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        const updatedUser = {
          sub: data.data.user.id,
          email: data.data.user.email,
          name: data.data.user.name,
          picture: data.data.user.avatar,
          email_verified: true,
          organizations: data.data.user.organizations || [],
        };

        // Update localStorage with fresh user data
        localStorage.setItem("auth_user", JSON.stringify(updatedUser));

        console.log("✅ User data refreshed with organizations:", {
          organizationsCount: updatedUser.organizations.length,
          organizations: updatedUser.organizations,
        });

        // Reload the page to update all contexts
        setTimeout(() => {
          window.location.reload();
        }, 500);
      } else {
        console.error("❌ Failed to refresh user data");
        // Still reload to trigger auth re-initialization
        setTimeout(() => {
          window.location.reload();
        }, 500);
      }
    } catch (error) {
      console.error("❌ Error refreshing user data:", error);
      // Still reload to trigger auth re-initialization
      setTimeout(() => {
        window.location.reload();
      }, 500);
    }
  };

  return {
    showModal,
    handleOrganizationCreated,
  };
}
