"use client";

import { useEffect, useState, useCallback } from "react";
import { usePusher } from "@/hooks/usePusher";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";

export interface VideoProcessingStatus {
  guideId: string;
  userId: string;
  organizationId: string;
  status:
    | "PENDING"
    | "DOWNLOADING"
    | "CONVERTING_TO_AUDIO"
    | "TRANSCRIBING"
    | "UPLOADING"
    | "GENERATING_SUMMARY"
    | "GENERATING_STEPS"
    | "COMPLETED"
    | "FAILED";
  progress?: number; // 0-100
  message?: string;
  error?: string;
  externalJobId?: string;
  estimatedTimeRemaining?: number; // seconds
  timestamp?: string;
}

export interface UseVideoProcessingOptions {
  guideId?: string;
  onStatusUpdate?: (status: VideoProcessingStatus) => void;
  onComplete?: (status: VideoProcessingStatus) => void;
  onError?: (status: VideoProcessingStatus) => void;
}

export function useVideoProcessing(options: UseVideoProcessingOptions = {}) {
  const { guideId, onStatusUpdate, onComplete, onError } = options;
  const pusher = usePusher();
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const [processingStatus, setProcessingStatus] =
    useState<VideoProcessingStatus | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingHistory, setProcessingHistory] = useState<
    VideoProcessingStatus[]
  >([]);

  const handleStatusUpdate = useCallback(
    (data: VideoProcessingStatus) => {
      console.log("Video processing status update:", data);

      // Filter by guideId if specified
      if (guideId && data.guideId !== guideId) {
        return;
      }

      setProcessingStatus(data);
      setProcessingHistory((prev) => [...prev, data]);

      // Update processing state
      const isCurrentlyProcessing = !["COMPLETED", "FAILED"].includes(
        data.status
      );
      setIsProcessing(isCurrentlyProcessing);

      // Call callbacks
      onStatusUpdate?.(data);

      if (data.status === "COMPLETED") {
        onComplete?.(data);
      } else if (data.status === "FAILED") {
        onError?.(data);
      }
    },
    [guideId, onStatusUpdate, onComplete, onError]
  );

  useEffect(() => {
    if (!pusher || !currentOrganization || !user) return;

    // Subscribe to user-specific channel for video processing updates
    const channelName = `private-org-${currentOrganization.id}-user-${user.sub}`;
    let channel = pusher.channel(channelName);

    if (!channel) {
      channel = pusher.subscribe(channelName);
    }

    // Bind to video processing events
    channel.bind("video-processing-status", handleStatusUpdate);

    return () => {
      if (channel) {
        channel.unbind("video-processing-status", handleStatusUpdate);
      }
    };
  }, [pusher, currentOrganization, user, handleStatusUpdate]);

  const clearProcessingHistory = useCallback(() => {
    setProcessingHistory([]);
    setProcessingStatus(null);
    setIsProcessing(false);
  }, []);

  const getProgressPercentage = useCallback(() => {
    return processingStatus?.progress || 0;
  }, [processingStatus]);

  const getStatusMessage = useCallback(() => {
    if (!processingStatus) return "";

    if (processingStatus.error) {
      return `Error: ${processingStatus.error}`;
    }

    return processingStatus.message || `Status: ${processingStatus.status}`;
  }, [processingStatus]);

  const getEstimatedTimeRemaining = useCallback(() => {
    return processingStatus?.estimatedTimeRemaining;
  }, [processingStatus]);

  return {
    // Current state
    processingStatus,
    isProcessing,
    processingHistory,

    // Computed values
    progress: getProgressPercentage(),
    statusMessage: getStatusMessage(),
    estimatedTimeRemaining: getEstimatedTimeRemaining(),

    // Actions
    clearProcessingHistory,
  };
}
