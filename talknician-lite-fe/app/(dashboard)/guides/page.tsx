"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { GuidesList } from "@/components/guides/GuidesList";
import { CreateGuideForm } from "@/components/guides/CreateGuideForm";

export default function GuidesPage() {
  const router = useRouter();
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const handleCreateGuide = () => {
    setShowCreateDialog(true);
  };

  const handleGuideCreated = (guideId: string) => {
    setShowCreateDialog(false);
    // Navigate to the newly created guide
    router.push(`/guides/${guideId}`);
  };

  const handleCancelCreate = () => {
    setShowCreateDialog(false);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <GuidesList onCreateGuide={handleCreateGuide} />

      {/* Create Guide Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Guide</DialogTitle>
          </DialogHeader>
          
          <CreateGuideForm
            onSuccess={handleGuideCreated}
            onCancel={handleCancelCreate}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
