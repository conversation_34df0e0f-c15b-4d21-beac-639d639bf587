"use client";

import React from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { ArrowLeft, Edit, Share2, MoreVertical } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { GuideViewer } from "@/components/guides/GuideViewer";
import { useAuth } from "@/contexts/AuthContext";
import { useQuery } from "@tanstack/react-query";
import { useGuideApi } from "@/api/guides";

export default function GuidePage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const guideApi = useGuideApi();
  const guideId = params.id as string;

  const {
    data: guide,
    isLoading,
  } = useQuery({
    queryKey: ['guide', guideId],
    queryFn: () => guideApi.getGuide(guideId),
    enabled: !!guideId,
  });

  const isOwner = user?.id === guide?.userId;

  const handleBack = () => {
    router.push('/guides');
  };

  const handleEdit = () => {
    router.push(`/guides/${guideId}/edit`);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: guide?.title || 'Guide',
          text: guide?.description || 'Check out this guide',
          url: window.location.href,
        });
      } catch (error) {
        // User cancelled sharing or sharing failed
        console.log('Sharing cancelled or failed:', error);
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href);
        // You could show a toast notification here
        alert('Link copied to clipboard!');
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  const handleDelete = async () => {
    if (!guide) return;
    
    if (confirm(`Are you sure you want to delete "${guide.title}"?`)) {
      try {
        await guideApi.deleteGuide(guideId);
        router.push('/guides');
      } catch (error) {
        console.error('Failed to delete guide:', error);
        alert('Failed to delete guide. Please try again.');
      }
    }
  };

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Guides
          </Button>
          
          {!isLoading && guide && (
            <div className="text-sm text-muted-foreground">
              {guide.title}
            </div>
          )}
        </div>

        {/* Actions */}
        {!isLoading && guide && (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShare}
              className="flex items-center gap-2"
            >
              <Share2 className="w-4 h-4" />
              Share
            </Button>

            {isOwner && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEdit}
                  className="flex items-center gap-2"
                >
                  <Edit className="w-4 h-4" />
                  Edit
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                      Delete Guide
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        )}
      </div>

      {/* Guide Viewer */}
      <GuideViewer guideId={guideId} />
    </div>
  );
}
