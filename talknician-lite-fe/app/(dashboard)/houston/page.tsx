"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useConversationApi } from "@/api/conversations";
import { getLastConversationId } from "@/utils/conversationStorage";
import { Loader2 } from "lucide-react";

export default function HoustonPage() {
  const router = useRouter();
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  const conversationApi = useConversationApi();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkLastConversation = async () => {
      // Wait for auth and organization to be ready
      if (!accessToken || !currentOrganization) {
        return;
      }

      try {
        const lastConversationId = getLastConversationId();

        if (lastConversationId) {
          // Verify the conversation still exists and user has access
          try {
            console.log("run load conversation get conversation");
            await conversationApi.getConversation(
              lastConversationId,
              currentOrganization.id
            );
            // If successful, redirect to the last conversation
            router.replace(`/houston/${lastConversationId}`);
            return;
          } catch (error) {
            // Conversation doesn't exist or no access, clear from storage
            console.warn("Last conversation no longer accessible:", error);
          }
        }

        // No valid last conversation, redirect to new conversation
        router.replace("/houston/new");
      } catch (error) {
        console.error("Error checking last conversation:", error);
        router.replace("/houston/new");
      } finally {
        setIsChecking(false);
      }
    };

    checkLastConversation();
  }, [accessToken, currentOrganization, router, conversationApi]);

  // Show loading while checking
  if (isChecking) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="flex items-center gap-2 text-slate-600">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span>Loading Houston...</span>
        </div>
      </div>
    );
  }

  return null; // This page just redirects
}
