"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Cloud,
  Loader2,
  AlertCircle,
  ChevronRight,
  LogOut,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useOneDrive } from "../../../hooks/data-integrations/useOneDrive";
import { useOrganization } from "@/contexts/OrganizationContext";
import OneDriveFileList from "./OneDriveFileList";
import { toast } from "sonner";

export default function OneDriveCard() {
  const [showFiles, setShowFiles] = useState(false);
  const [disconnecting, setDisconnecting] = useState(false);
  const [connectionState, setConnectionState] = useState({
    connected: null as boolean | null,
    loading: true,
    connecting: false,
    error: "",
  });

  const { currentOrganization } = useOrganization();
  const { oneDriveApi, navigateToFolder } = useOneDrive();

  // Check OneDrive connection status
  const checkConnection = useCallback(async () => {
    try {
      setConnectionState((prev) => ({ ...prev, loading: true, error: "" }));
      const connected = await oneDriveApi.isOneDriveConnected();
      setConnectionState((prev) => ({
        ...prev,
        connected,
        loading: false,
      }));
    } catch (error) {
      console.error("Error checking OneDrive connection:", error);
      setConnectionState((prev) => ({
        ...prev,
        connected: false,
        loading: false,
        error: "Failed to check connection status",
      }));
    }
  }, [oneDriveApi]);

  // Connect to OneDrive
  const connectOneDrive = () => {
    if (!currentOrganization) {
      toast.error("Please select an organization first");
      return;
    }
    window.location.href = oneDriveApi.getOneDriveAuthUrl();
  };

  // Initialize on mount
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  // Load root folder when connected
  useEffect(() => {
    if (connectionState.connected && currentOrganization) {
      navigateToFolder("/");
    }
  }, [connectionState.connected, currentOrganization, navigateToFolder]);

  const handleDisconnect = async () => {
    setDisconnecting(true);
    try {
      await oneDriveApi.disconnectOneDrive();
      toast.success("OneDrive disconnected");
      setShowFiles(false);
      checkConnection();
    } catch (error) {
      toast.error("Failed to disconnect OneDrive");
    } finally {
      setDisconnecting(false);
    }
  };

  const renderConnectionStatus = () => {
    if (connectionState.loading) {
      return (
        <div className="flex items-center space-x-2">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span className="text-sm text-slate-600">Checking connection...</span>
        </div>
      );
    }

    if (connectionState.error) {
      return (
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-4 h-4 text-red-500" />
          <span className="text-sm text-red-600">{connectionState.error}</span>
        </div>
      );
    }

    if (connectionState.connected) {
      return (
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-2">
            <Badge className="bg-green-100 text-green-800 border-green-200">
              Connected
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiles(!showFiles)}
              className="ml-2"
            >
              {showFiles ? "Hide Files" : "Browse Files"}
              <ChevronRight
                className={`w-4 h-4 ml-1 transition-transform ${
                  showFiles ? "rotate-90" : ""
                }`}
              />
            </Button>
          </div>
          <Button
            variant="destructive"
            size="sm"
            onClick={handleDisconnect}
            disabled={disconnecting}
            className="ml-2"
          >
            {disconnecting ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <LogOut className="w-4 h-4 mr-2" />
            )}
            Disconnect
          </Button>
        </div>
      );
    }

    return (
      <Button
        onClick={connectOneDrive}
        disabled={connectionState.connecting}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
      >
        {connectionState.connecting ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Connecting...
          </>
        ) : (
          <>
            <Cloud className="w-4 h-4 mr-2" />
            Connect OneDrive
          </>
        )}
      </Button>
    );
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-lg">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Cloud className="w-5 h-5 text-blue-600" />
          </div>
          <span>OneDrive</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-slate-600 dark:text-slate-400">
          Access and manage your OneDrive files directly from Talknician.
        </p>

        {renderConnectionStatus()}

        {showFiles && connectionState.connected && (
          <div className="mt-4 border-t pt-4">
            <OneDriveFileList />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
