"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface TabNavigationProps {
  currentTab: string;
  onTabChange: (value: string) => void;
}

export default function TabNavigation({
  currentTab,
  onTabChange,
}: TabNavigationProps) {
  return (
    <TabsList className="grid w-full grid-cols-5 mb-4 sm:mb-6">
      <TabsTrigger value="files" className="text-xs sm:text-sm">
        Files
      </TabsTrigger>
      <TabsTrigger value="cloud" className="text-xs sm:text-sm">
        Data Integrations
      </TabsTrigger>
      <TabsTrigger value="websites" className="text-xs sm:text-sm">
        Websites
      </TabsTrigger>
      <TabsTrigger value="guides" className="text-xs sm:text-sm">
        Guides
      </TabsTrigger>
      <TabsTrigger value="rag" className="text-xs sm:text-sm">
        Houston Document Processing
      </TabsTrigger>
    </TabsList>
  );
}
