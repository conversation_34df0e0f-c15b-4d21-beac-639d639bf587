"use client";

import { useState } from "react";
import { Tabs } from "@/components/ui/tabs";
import SearchBar from "./components/shared/SearchBar";
import TabNavigation from "./components/shared/TabNavigation";
import FilesTab from "./components/files/FilesTab";
import DataIntegrationsTab from "./components/data-integrations/DataIntegrationsTab";
import WebsitesTab from "./components/websites/WebsitesTab";
import GuidesTab from "./components/guides/GuidesTab";
import HoustonTab from "./components/houston/HoustonTab";
import { useRouter, useSearchParams } from "next/navigation";

export default function IntegrationsPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const searchParams = useSearchParams();
  const router = useRouter();
  const currentTab = searchParams.get("tab") || "files";

  const handleTabChange = (tab: string) => {
    router.push(`/integrations?tab=${tab}`);
  };

  return (
    <div className="flex flex-col h-full bg-slate-50 dark:bg-slate-900">
      <div className="flex-none p-4 sm:p-6 bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-slate-900 dark:text-white">
                Knowledge Base
              </h1>
              <p className="text-slate-600 dark:text-slate-400 mt-1">
                Manage your files, data sources, and AI processing
              </p>
            </div>
          </div>

          <div className="mb-4">
            <SearchBar
              value={searchQuery}
              onChange={setSearchQuery}
              placeholder="Search files and integrations..."
            />
          </div>
        </div>
      </div>

      <div className="flex-1 p-4 sm:p-6  overflow-auto h-full">
        <div className="max-w-7xl mx-auto h-full">
          <Tabs
            value={currentTab}
            onValueChange={handleTabChange}
            className="h-full flex flex-col"
          >
            <TabNavigation
              currentTab={currentTab}
              onTabChange={handleTabChange}
            />

            <div className="flex-1 min-h-0">
              <FilesTab searchQuery={searchQuery} />
              <DataIntegrationsTab />
              <WebsitesTab />
              <GuidesTab searchQuery={searchQuery} />
              <HoustonTab />
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
