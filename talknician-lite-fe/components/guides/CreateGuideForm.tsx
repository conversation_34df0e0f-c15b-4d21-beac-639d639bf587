"use client";

import React, { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import {
  Upload,
  Video,
  X,
  FileVideo,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { VideoProcessingProgress } from "@/components/video-processing/VideoProcessingProgress";
import { useGuideApi, type CreateGuideRequest } from "@/api/guides";
import { useVideoProcessing } from "@/hooks/useVideoProcessing";
import { useOrganization } from "@/contexts/OrganizationContext";

interface CreateGuideFormProps {
  onSuccess?: (guideId: string) => void;
  onCancel?: () => void;
  className?: string;
}

const MAX_FILE_SIZE = 500 * 1024 * 1024; // 500MB
const ACCEPTED_VIDEO_TYPES = [
  "video/mp4",
  "video/avi",
  "video/mov",
  "video/wmv",
  "video/flv",
  "video/webm",
  "video/mkv",
];

export function CreateGuideForm({
  onSuccess,
  onCancel,
  className = "",
}: CreateGuideFormProps) {
  const router = useRouter();
  const guideApi = useGuideApi();
  const { currentOrganization } = useOrganization();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    visibility: "ORGANIZATION" as const,
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [createdGuideId, setCreatedGuideId] = useState<string | null>(null);

  // Video processing hook for real-time updates
  const { isProcessing, processingStatus, progress, statusMessage } =
    useVideoProcessing({
      guideId: createdGuideId || undefined,
      onComplete: (status) => {
        console.log("Guide processing completed:", status);
        if (createdGuideId) {
          onSuccess?.(createdGuideId);
        }
      },
      onError: (status) => {
        console.error("Guide processing failed:", status);
        setValidationErrors([status.error || "Processing failed"]);
      },
    });

  const createGuideMutation = useMutation({
    mutationFn: (data: CreateGuideRequest) =>
      guideApi.createGuideFromVideo(data),
    onSuccess: (response) => {
      console.log("Guide created successfully:", response.guide);
      setCreatedGuideId(response.guide.id);
      // Don't call onSuccess here - wait for processing to complete
    },
    onError: (error: any) => {
      console.error("Failed to create guide:", error);
      setValidationErrors([
        error.response?.data?.message ||
          "Failed to create guide. Please try again.",
      ]);
    },
  });

  const validateFile = (file: File): string[] => {
    const errors: string[] = [];

    if (!ACCEPTED_VIDEO_TYPES.includes(file.type)) {
      errors.push(
        "Please select a valid video file (MP4, AVI, MOV, WMV, FLV, WebM, MKV)"
      );
    }

    if (file.size > MAX_FILE_SIZE) {
      errors.push(
        `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`
      );
    }

    return errors;
  };

  const handleFileSelect = (file: File) => {
    const errors = validateFile(file);
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setSelectedFile(file);
    setValidationErrors([]);

    // Auto-generate title from filename if title is empty
    if (!formData.title) {
      const nameWithoutExtension = file.name.replace(/\.[^/.]+$/, "");
      setFormData((prev) => ({
        ...prev,
        title: nameWithoutExtension
          .replace(/[_-]/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase()),
      }));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors: string[] = [];

    if (!formData.title.trim()) {
      errors.push("Title is required");
    }

    if (!selectedFile) {
      errors.push("Please select a video file");
    }

    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setValidationErrors([]);

    if (!currentOrganization) {
      setValidationErrors(["No organization selected"]);
      return;
    }

    createGuideMutation.mutate({
      title: formData.title.trim(),
      description: formData.description.trim() || undefined,
      visibility: formData.visibility,
      videoFile: selectedFile!,
      organizationId: currentOrganization.id,
    });
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const isSubmitting = createGuideMutation.isPending;
  const showProcessing = createdGuideId && isProcessing;

  return (
    <div className={`max-w-2xl mx-auto ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="w-5 h-5" />
            Create Guide from Video
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Show processing progress if guide is being processed */}
          {showProcessing && (
            <VideoProcessingProgress
              guideId={createdGuideId}
              onComplete={() => {
                if (createdGuideId) {
                  onSuccess?.(createdGuideId);
                }
              }}
              onError={(error) => {
                setValidationErrors([error]);
              }}
            />
          )}

          {/* Show form only if not processing */}
          {!showProcessing && (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Validation Errors */}
              {validationErrors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <ul className="list-disc list-inside space-y-1">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}

              {/* File Upload */}
              <div className="space-y-2">
                <Label htmlFor="video-upload">Video File</Label>

                {!selectedFile ? (
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      dragActive
                        ? "border-primary bg-primary/5"
                        : "border-muted-foreground/25 hover:border-muted-foreground/50"
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    <div className="space-y-2">
                      <p className="text-lg font-medium">
                        Drop your video file here, or{" "}
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="text-primary hover:underline"
                        >
                          browse
                        </button>
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Supports MP4, AVI, MOV, WMV, FLV, WebM, MKV (max 500MB)
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <FileVideo className="w-8 h-8 text-primary" />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium truncate">
                          {selectedFile.name}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {formatFileSize(selectedFile.size)}
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleRemoveFile}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                )}

                <input
                  ref={fileInputRef}
                  type="file"
                  accept={ACCEPTED_VIDEO_TYPES.join(",")}
                  onChange={handleFileInputChange}
                  className="hidden"
                  id="video-upload"
                />
              </div>

              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, title: e.target.value }))
                  }
                  placeholder="Enter guide title..."
                  required
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  placeholder="Describe what this guide teaches..."
                  rows={3}
                />
              </div>

              {/* Visibility */}
              <div className="space-y-2">
                <Label htmlFor="visibility">Visibility</Label>
                <Select
                  value={formData.visibility}
                  onValueChange={(value: any) =>
                    setFormData((prev) => ({ ...prev, visibility: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PRIVATE">
                      Private - Only you can see this
                    </SelectItem>
                    <SelectItem value="ORGANIZATION">
                      Organization - Anyone in your organization
                    </SelectItem>
                    <SelectItem value="PUBLIC">
                      Public - Anyone can see this
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Actions */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="submit"
                  disabled={
                    isSubmitting || !selectedFile || !formData.title.trim()
                  }
                  className="flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating Guide...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Create Guide
                    </>
                  )}
                </Button>

                {onCancel && (
                  <Button type="button" variant="outline" onClick={onCancel}>
                    Cancel
                  </Button>
                )}
              </div>
            </form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
