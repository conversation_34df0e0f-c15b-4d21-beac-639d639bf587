"use client";

import React, { useState, useRef, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward, 
  Volume2, 
  Maximize, 
  Clock,
  User,
  Calendar,
  Eye,
  Users,
  Globe,
  ChevronRight,
  CheckCircle,
  Circle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Slider } from "@/components/ui/slider";
import { useGuideApi, type Guide } from "@/api/guides";
import { formatDistanceToNow } from "date-fns";

interface GuideViewerProps {
  guideId: string;
  className?: string;
}

const statusColors = {
  DRAFT: "bg-gray-500",
  PROCESSING: "bg-blue-500",
  REVIEW: "bg-yellow-500",
  PUBLISHED: "bg-green-500",
  ARCHIVED: "bg-red-500",
};

const statusLabels = {
  DRAFT: "Draft",
  PROCESSING: "Processing",
  REVIEW: "Review",
  PUBLISHED: "Published",
  ARCHIVED: "Archived",
};

const visibilityIcons = {
  PRIVATE: Eye,
  ORGANIZATION: Users,
  PUBLIC: Globe,
};

const difficultyColors = {
  beginner: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  intermediate: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  advanced: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export function GuideViewer({ guideId, className = "" }: GuideViewerProps) {
  const guideApi = useGuideApi();
  const videoRef = useRef<HTMLVideoElement>(null);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [activeStepIndex, setActiveStepIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());

  const {
    data: guide,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['guide', guideId],
    queryFn: () => guideApi.getGuide(guideId),
    enabled: !!guideId,
  });

  // Update active step based on video time
  useEffect(() => {
    if (!guide?.steps || guide.steps.length === 0) return;

    const currentStep = guide.steps.findIndex((step, index) => {
      const nextStep = guide.steps[index + 1];
      const stepStart = step.startTime || 0;
      const stepEnd = nextStep?.startTime || duration;
      
      return currentTime >= stepStart && currentTime < stepEnd;
    });

    if (currentStep !== -1 && currentStep !== activeStepIndex) {
      setActiveStepIndex(currentStep);
    }
  }, [currentTime, guide?.steps, duration, activeStepIndex]);

  const handlePlayPause = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (!videoRef.current) return;
    setCurrentTime(videoRef.current.currentTime);
  };

  const handleLoadedMetadata = () => {
    if (!videoRef.current) return;
    setDuration(videoRef.current.duration);
  };

  const handleSeek = (time: number) => {
    if (!videoRef.current) return;
    videoRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleStepClick = (stepIndex: number) => {
    const step = guide?.steps[stepIndex];
    if (!step || !step.startTime) return;

    handleSeek(step.startTime);
    setActiveStepIndex(stepIndex);
  };

  const handleStepComplete = (stepIndex: number) => {
    setCompletedSteps(prev => new Set([...prev, stepIndex]));
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null;
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p>Loading guide...</p>
        </div>
      </div>
    );
  }

  if (error || !guide) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">Failed to load guide</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const VisibilityIcon = visibilityIcons[guide.visibility];

  return (
    <div className={`max-w-7xl mx-auto ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Video Player and Guide Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Video Player */}
          {guide.videoUrl && (
            <Card className="overflow-hidden">
              <div className="relative aspect-video bg-black">
                <video
                  ref={videoRef}
                  src={guide.videoUrl}
                  className="w-full h-full"
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                />
                
                {/* Video Controls */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                  <div className="space-y-2">
                    {/* Progress Bar */}
                    <Slider
                      value={[currentTime]}
                      max={duration}
                      step={1}
                      onValueChange={([value]) => handleSeek(value)}
                      className="w-full"
                    />
                    
                    {/* Controls */}
                    <div className="flex items-center justify-between text-white">
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handlePlayPause}
                          className="text-white hover:bg-white/20"
                        >
                          {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </Button>
                        
                        <span className="text-sm">
                          {formatTime(currentTime)} / {formatTime(duration)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Volume2 className="w-4 h-4" />
                        <Slider
                          value={[volume]}
                          max={1}
                          step={0.1}
                          onValueChange={([value]) => {
                            setVolume(value);
                            if (videoRef.current) {
                              videoRef.current.volume = value;
                            }
                          }}
                          className="w-20"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Guide Information */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="text-2xl">{guide.title}</CardTitle>
                  {guide.description && (
                    <p className="text-muted-foreground">{guide.description}</p>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className={statusColors[guide.status]}>
                    {statusLabels[guide.status]}
                  </Badge>
                  
                  {guide.difficulty && (
                    <Badge variant="outline" className={difficultyColors[guide.difficulty]}>
                      {guide.difficulty}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Guide Metadata */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4 text-muted-foreground" />
                  <span>{guide.user.name}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <span>{formatDistanceToNow(new Date(guide.createdAt), { addSuffix: true })}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <VisibilityIcon className="w-4 h-4 text-muted-foreground" />
                  <span className="capitalize">{guide.visibility.toLowerCase()}</span>
                </div>
                
                {guide.estimatedDuration && (
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>{formatDuration(guide.estimatedDuration)}</span>
                  </div>
                )}
              </div>

              {/* Summary */}
              {guide.summary && (
                <>
                  <Separator />
                  <div>
                    <h3 className="font-semibold mb-2">Summary</h3>
                    <p className="text-muted-foreground">{guide.summary}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Steps Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ChevronRight className="w-5 h-5" />
                Guide Steps
                {guide.steps.length > 0 && (
                  <Badge variant="outline" className="ml-auto">
                    {completedSteps.size}/{guide.steps.length}
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div className="p-4 space-y-3">
                  {guide.steps.length === 0 ? (
                    <p className="text-muted-foreground text-center py-8">
                      No steps available for this guide.
                    </p>
                  ) : (
                    guide.steps.map((step, index) => {
                      const isActive = index === activeStepIndex;
                      const isCompleted = completedSteps.has(index);
                      
                      return (
                        <div
                          key={step.id}
                          className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                            isActive 
                              ? 'border-primary bg-primary/5' 
                              : 'border-border hover:border-primary/50'
                          }`}
                          onClick={() => handleStepClick(index)}
                        >
                          <div className="flex items-start gap-3">
                            <div className="flex-shrink-0 mt-1">
                              {isCompleted ? (
                                <CheckCircle className="w-5 h-5 text-green-500" />
                              ) : (
                                <Circle className="w-5 h-5 text-muted-foreground" />
                              )}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-xs font-medium text-muted-foreground">
                                  Step {index + 1}
                                </span>
                                {step.startTime !== undefined && (
                                  <span className="text-xs text-muted-foreground">
                                    {formatTime(step.startTime)}
                                  </span>
                                )}
                              </div>
                              
                              <h4 className="font-medium text-sm mb-1">{step.title}</h4>
                              
                              {step.description && (
                                <p className="text-xs text-muted-foreground line-clamp-2">
                                  {step.description}
                                </p>
                              )}
                              
                              {!isCompleted && isActive && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="mt-2 text-xs"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleStepComplete(index);
                                  }}
                                >
                                  Mark Complete
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
