"use client";

import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { 
  Search, 
  Filter, 
  Plus, 
  Grid, 
  List, 
  Eye, 
  Users, 
  Globe,
  Clock,
  Play,
  MoreVertical,
  Edit,
  Trash2
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { useGuideApi, type Guide, type GuideFilters } from "@/api/guides";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";
import { formatDistanceToNow } from "date-fns";

interface GuidesListProps {
  onCreateGuide?: () => void;
  className?: string;
}

const statusColors = {
  DRAFT: "bg-gray-500",
  PROCESSING: "bg-blue-500",
  REVIEW: "bg-yellow-500",
  PUBLISHED: "bg-green-500",
  ARCHIVED: "bg-red-500",
};

const statusLabels = {
  DRAFT: "Draft",
  PROCESSING: "Processing",
  REVIEW: "Review",
  PUBLISHED: "Published",
  ARCHIVED: "Archived",
};

const visibilityIcons = {
  PRIVATE: Eye,
  ORGANIZATION: Users,
  PUBLIC: Globe,
};

const difficultyColors = {
  beginner: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
  intermediate: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
  advanced: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
};

export function GuidesList({ onCreateGuide, className = "" }: GuidesListProps) {
  const router = useRouter();
  const guideApi = useGuideApi();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  const [filters, setFilters] = useState<GuideFilters>({
    page: 1,
    limit: 12,
  });
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: searchQuery, page: 1 }));
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const {
    data: guidesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['guides', currentOrganization?.id, filters],
    queryFn: () => guideApi.getGuides(currentOrganization!.id, filters),
    enabled: !!currentOrganization,
    staleTime: 30000, // 30 seconds
  });

  const handleFilterChange = (key: keyof GuideFilters, value: string | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
      page: 1, // Reset to first page when filtering
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleGuideClick = (guide: Guide) => {
    router.push(`/guides/${guide.id}`);
  };

  const handleEditGuide = (guide: Guide, e: React.MouseEvent) => {
    e.stopPropagation();
    router.push(`/guides/${guide.id}/edit`);
  };

  const handleDeleteGuide = async (guide: Guide, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm(`Are you sure you want to delete "${guide.title}"?`)) {
      try {
        await guideApi.deleteGuide(guide.id);
        refetch();
      } catch (error) {
        console.error('Failed to delete guide:', error);
      }
    }
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null;
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-500 mb-2">Failed to load guides</p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Guides</h1>
          <p className="text-muted-foreground">
            Create and manage instructional guides from videos
          </p>
        </div>
        <Button onClick={onCreateGuide} className="flex items-center gap-2">
          <Plus className="w-4 h-4" />
          Create Guide
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search guides..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Select
            value={filters.status || "all"}
            onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="DRAFT">Draft</SelectItem>
              <SelectItem value="PROCESSING">Processing</SelectItem>
              <SelectItem value="REVIEW">Review</SelectItem>
              <SelectItem value="PUBLISHED">Published</SelectItem>
              <SelectItem value="ARCHIVED">Archived</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.visibility || "all"}
            onValueChange={(value) => handleFilterChange('visibility', value === 'all' ? undefined : value)}
          >
            <SelectTrigger className="w-36">
              <SelectValue placeholder="Visibility" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Visibility</SelectItem>
              <SelectItem value="PRIVATE">Private</SelectItem>
              <SelectItem value="ORGANIZATION">Organization</SelectItem>
              <SelectItem value="PUBLIC">Public</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Guides Grid/List */}
      {!isLoading && guidesData && (
        <>
          {guidesData.guides.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-muted-foreground mb-4">
                {filters.search ? 'No guides found matching your search.' : 'No guides created yet.'}
              </div>
              {!filters.search && (
                <Button onClick={onCreateGuide} variant="outline">
                  Create Your First Guide
                </Button>
              )}
            </div>
          ) : (
            <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
              {guidesData.guides.map((guide) => (
                <GuideCard
                  key={guide.id}
                  guide={guide}
                  viewMode={viewMode}
                  currentUserId={user?.id}
                  onClick={() => handleGuideClick(guide)}
                  onEdit={(e) => handleEditGuide(guide, e)}
                  onDelete={(e) => handleDeleteGuide(guide, e)}
                />
              ))}
            </div>
          )}

          {/* Pagination */}
          {guidesData.totalPages > 1 && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                disabled={guidesData.page <= 1}
                onClick={() => handlePageChange(guidesData.page - 1)}
              >
                Previous
              </Button>
              
              <div className="flex items-center gap-2">
                {Array.from({ length: Math.min(5, guidesData.totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={page === guidesData.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Button>
                  );
                })}
              </div>

              <Button
                variant="outline"
                disabled={guidesData.page >= guidesData.totalPages}
                onClick={() => handlePageChange(guidesData.page + 1)}
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}

interface GuideCardProps {
  guide: Guide;
  viewMode: 'grid' | 'list';
  currentUserId?: string;
  onClick: () => void;
  onEdit: (e: React.MouseEvent) => void;
  onDelete: (e: React.MouseEvent) => void;
}

function GuideCard({ guide, viewMode, currentUserId, onClick, onEdit, onDelete }: GuideCardProps) {
  const VisibilityIcon = visibilityIcons[guide.visibility];
  const isOwner = currentUserId === guide.userId;

  const formatDuration = (seconds?: number) => {
    if (!seconds) return null;
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  };

  if (viewMode === 'list') {
    return (
      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onClick}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="w-24 h-16 bg-muted rounded-md flex items-center justify-center">
              <Play className="w-6 h-6 text-muted-foreground" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold truncate">{guide.title}</h3>
                  <p className="text-sm text-muted-foreground truncate">
                    {guide.description || 'No description'}
                  </p>
                </div>
                
                <div className="flex items-center gap-2 ml-4">
                  <Badge variant="secondary" className={statusColors[guide.status]}>
                    {statusLabels[guide.status]}
                  </Badge>
                  
                  {isOwner && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={onEdit}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={onDelete} className="text-red-600">
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <VisibilityIcon className="w-3 h-3" />
                  <span className="capitalize">{guide.visibility.toLowerCase()}</span>
                </div>
                
                {guide.estimatedDuration && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDuration(guide.estimatedDuration)}</span>
                  </div>
                )}
                
                <span>
                  {formatDistanceToNow(new Date(guide.createdAt), { addSuffix: true })}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow overflow-hidden" onClick={onClick}>
      <div className="aspect-video bg-muted flex items-center justify-center">
        <Play className="w-12 h-12 text-muted-foreground" />
      </div>
      
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-semibold truncate flex-1">{guide.title}</h3>
          
          {isOwner && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                  <MoreVertical className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onEdit}>
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onDelete} className="text-red-600">
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        
        <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
          {guide.description || 'No description'}
        </p>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className={statusColors[guide.status]}>
              {statusLabels[guide.status]}
            </Badge>
            
            {guide.difficulty && (
              <Badge variant="outline" className={difficultyColors[guide.difficulty]}>
                {guide.difficulty}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <VisibilityIcon className="w-3 h-3" />
            <span className="capitalize">{guide.visibility.toLowerCase()}</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-3 pt-3 border-t">
          <div className="flex items-center gap-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={guide.user.avatar} />
              <AvatarFallback className="text-xs">
                {guide.user.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <span className="text-xs text-muted-foreground truncate">
              {guide.user.name}
            </span>
          </div>
          
          {guide.estimatedDuration && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>{formatDuration(guide.estimatedDuration)}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
