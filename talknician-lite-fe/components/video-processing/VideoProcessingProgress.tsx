"use client";

import React from "react";
import { Progress } from "@/components/ui/progress";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Clock,
  Download,
  Music,
  FileText,
  Sparkles,
  List
} from "lucide-react";
import { useVideoProcessing, VideoProcessingStatus } from "@/hooks/useVideoProcessing";

interface VideoProcessingProgressProps {
  guideId: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

const statusIcons = {
  PENDING: Loader2,
  DOWNLOADING: Download,
  CONVERTING_TO_AUDIO: Music,
  TRANSCRIBING: FileText,
  UPLOADING: CheckCircle,
  GENERATING_SUMMARY: Sparkles,
  GENERATING_STEPS: List,
  COMPLETED: CheckCircle,
  FAILED: XCircle,
};

const statusColors = {
  PENDING: "bg-blue-500",
  DOWNLOADING: "bg-blue-500",
  CONVERTING_TO_AUDIO: "bg-purple-500",
  TRANSCRIBING: "bg-orange-500",
  UPLOADING: "bg-green-500",
  GENERATING_SUMMARY: "bg-pink-500",
  GENERATING_STEPS: "bg-indigo-500",
  COMPLETED: "bg-green-500",
  FAILED: "bg-red-500",
};

const statusLabels = {
  PENDING: "Initializing",
  DOWNLOADING: "Downloading Video",
  CONVERTING_TO_AUDIO: "Extracting Audio",
  TRANSCRIBING: "Transcribing Audio",
  UPLOADING: "Uploading Results",
  GENERATING_SUMMARY: "Generating Summary",
  GENERATING_STEPS: "Creating Steps",
  COMPLETED: "Completed",
  FAILED: "Failed",
};

export function VideoProcessingProgress({ 
  guideId, 
  onComplete, 
  onError, 
  className = "" 
}: VideoProcessingProgressProps) {
  const {
    processingStatus,
    isProcessing,
    progress,
    statusMessage,
    estimatedTimeRemaining,
  } = useVideoProcessing({
    guideId,
    onComplete: (status) => {
      console.log("Video processing completed:", status);
      onComplete?.();
    },
    onError: (status) => {
      console.log("Video processing failed:", status);
      onError?.(status.error || "Processing failed");
    },
  });

  if (!processingStatus && !isProcessing) {
    return null;
  }

  const currentStatus = processingStatus?.status || 'PENDING';
  const StatusIcon = statusIcons[currentStatus];
  const isCompleted = currentStatus === 'COMPLETED';
  const isFailed = currentStatus === 'FAILED';
  const isActive = isProcessing && !isCompleted && !isFailed;

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <StatusIcon 
            className={`w-5 h-5 ${isActive ? 'animate-spin' : ''} ${
              isCompleted ? 'text-green-500' : 
              isFailed ? 'text-red-500' : 
              'text-blue-500'
            }`} 
          />
          Video Processing
          <Badge 
            variant={isCompleted ? "default" : isFailed ? "destructive" : "secondary"}
            className="ml-auto"
          >
            {statusLabels[currentStatus]}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{progress}%</span>
          </div>
          <Progress 
            value={progress} 
            className="h-2"
            indicatorClassName={statusColors[currentStatus]}
          />
        </div>

        {/* Status Message */}
        {statusMessage && (
          <div className="text-sm text-muted-foreground">
            {statusMessage}
          </div>
        )}

        {/* Estimated Time Remaining */}
        {estimatedTimeRemaining && estimatedTimeRemaining > 0 && isActive && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="w-4 h-4" />
            <span>Estimated time remaining: {formatTime(estimatedTimeRemaining)}</span>
          </div>
        )}

        {/* Processing Steps */}
        <div className="space-y-2">
          <div className="text-sm font-medium text-muted-foreground">Processing Steps:</div>
          <div className="grid grid-cols-1 gap-1">
            {Object.entries(statusLabels).map(([status, label]) => {
              if (status === 'FAILED') return null;
              
              const isCurrentStep = currentStatus === status;
              const isCompletedStep = Object.keys(statusLabels).indexOf(currentStatus) > 
                                     Object.keys(statusLabels).indexOf(status);
              const StepIcon = statusIcons[status as keyof typeof statusIcons];
              
              return (
                <div 
                  key={status}
                  className={`flex items-center gap-2 text-xs p-2 rounded ${
                    isCurrentStep ? 'bg-blue-50 dark:bg-blue-950 text-blue-700 dark:text-blue-300' :
                    isCompletedStep ? 'bg-green-50 dark:bg-green-950 text-green-700 dark:text-green-300' :
                    'text-muted-foreground'
                  }`}
                >
                  <StepIcon className={`w-3 h-3 ${
                    isCurrentStep && isActive ? 'animate-spin' : ''
                  }`} />
                  <span>{label}</span>
                  {isCompletedStep && (
                    <CheckCircle className="w-3 h-3 text-green-500 ml-auto" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Error Message */}
        {isFailed && processingStatus?.error && (
          <div className="p-3 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-md">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
              <XCircle className="w-4 h-4" />
              <span className="text-sm font-medium">Processing Failed</span>
            </div>
            <div className="text-sm text-red-600 dark:text-red-400 mt-1">
              {processingStatus.error}
            </div>
          </div>
        )}

        {/* Success Message */}
        {isCompleted && (
          <div className="p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-md">
            <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm font-medium">Processing Completed Successfully!</span>
            </div>
            <div className="text-sm text-green-600 dark:text-green-400 mt-1">
              Your guide has been generated and is ready for review.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
