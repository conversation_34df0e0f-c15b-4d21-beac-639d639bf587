"use client";

import { useEffect } from "react";
import { usePusher } from "@/hooks/usePusher";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useOneDriveStore } from "@/stores/useOneDriveStore";

export function PusherListener() {
  const pusher = usePusher();
  const { accessToken } = useAuth();
  const { currentOrganization } = useOrganization();
  const { navigateToFolder, path } = useOneDriveStore();

  useEffect(() => {
    if (!pusher || !currentOrganization) return;

    const channelName = `private-org-${currentOrganization.id}`;
    let channel = pusher.channel(channelName);
    if (!channel) {
      channel = pusher.subscribe(channelName);
    }

    const fileStatusUpdateHandler = (data: any) => {
      console.log("Pusher event received: file-status-update", data);
      // Refresh the current folder
      navigateToFolder(path, currentOrganization.id, accessToken);
    };

    const fileDeletedHandler = (data: any) => {
      console.log("Pusher event received: file-deleted-from-rag", data);
      // Refresh the current folder
      navigateToFolder(path, currentOrganization.id, accessToken);
    };

    const videoProcessingHandler = (data: any) => {
      console.log("Pusher event received: video-processing-status", data);
      // Video processing updates are handled by the useVideoProcessing hook
      // This is just for logging/debugging
    };

    channel.bind("file-status-update", fileStatusUpdateHandler);
    channel.bind("file-deleted-from-rag", fileDeletedHandler);
    channel.bind("video-processing-status", videoProcessingHandler);

    return () => {
      if (channel) {
        channel.unbind("file-status-update", fileStatusUpdateHandler);
        channel.unbind("file-deleted-from-rag", fileDeletedHandler);
        channel.unbind("video-processing-status", videoProcessingHandler);
        // We don't unsubscribe here, as the main hook manages the connection lifecycle
      }
    };
  }, [pusher, currentOrganization, navigateToFolder, path, accessToken]);

  return null; // This is a listener component, it doesn't render anything
}
