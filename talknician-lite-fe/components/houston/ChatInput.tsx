"use client";

import React, {
  KeyboardEvent,
  useState,
  useRef,
  ChangeEvent,
  useEffect,
} from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Send,
  Globe,
  Loader2,
  Square,
  FileText,
  Paperclip,
  X,
  Lock,
  Users,
  Mic,
  MicOff,
} from "lucide-react";
import { useOrganization } from "@/contexts/OrganizationContext";
import { useMessageDocumentApi } from "@/api/message-document";
import type { MessageDocument } from "@/api/message-document";
import { ConversationVisibility } from "@/types/chat";
import { useSpeechToText } from "@/hooks/useSpeechToText";
import { toast } from "sonner";

export interface UploadedFileState extends Partial<MessageDocument> {
  id: string;
  name: string;
  status: "uploading" | "completed" | "error";
  error?: string;
}

interface ChatInputProps {
  onSend: (
    message: string,
    files?: UploadedFileState[],
    visibility?: ConversationVisibility
  ) => void;
  onStop?: () => void;
  isStreaming: boolean;
  isLoading?: boolean;
  webSearchEnabled: boolean;
  onWebSearchToggle: (enabled: boolean) => void;
  fileSearchEnabled: boolean;
  onFileSearchToggle: (enabled: boolean) => void;
  disabled?: boolean;
  placeholder?: string;
  isNewConversation?: boolean;
  showVisibilityControls?: boolean;
  currentUserId?: string;
  conversationOwnerId?: string;
  conversationOwnerName?: string;
}

export function ChatInput({
  onSend,
  onStop,
  isStreaming,
  isLoading = false,
  webSearchEnabled,
  onWebSearchToggle,
  disabled = false,
  placeholder = "Ask Houston anything...",
  isNewConversation = false,
  showVisibilityControls = false,
  currentUserId,
  conversationOwnerId,
  conversationOwnerName,
}: ChatInputProps) {
  const [localValue, setLocalValue] = useState("");
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFileState[]>([]);
  const [conversationVisibility, setConversationVisibility] =
    useState<ConversationVisibility>("private");

  // Speech-to-text functionality
  const {
    transcript,
    interimTranscript,
    isListening,
    isSupported: isSpeechSupported,
    error: speechError,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechToText({
    continuous: true,
    interimResults: true,
    language: "en-US",
  });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { currentOrganization } = useOrganization();
  const messageDocumentApi = useMessageDocumentApi();

  // Check if user is the conversation owner
  const isConversationOwner =
    !conversationOwnerId || currentUserId === conversationOwnerId;
  const isInputDisabled =
    disabled || (!isConversationOwner && !isNewConversation);

  // Handle speech transcript updates
  useEffect(() => {
    if (transcript) {
      // Append transcript to existing text
      setLocalValue((prev) => {
        const newValue = prev + (prev ? " " : "") + transcript;
        return newValue;
      });
      resetTranscript(); // Clear the transcript after adding it
    }
  }, [transcript, resetTranscript]);

  // Show speech errors as toasts
  useEffect(() => {
    if (speechError) {
      toast.error(speechError);
    }
  }, [speechError]);

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    if (files.length + uploadedFiles.length > 10) {
      toast.error("Upload limit exceeded");
      return;
    }

    if (!currentOrganization) {
      toast.error("Please select an organization first");
      return;
    }

    const newFilesToUpload = Array.from(files);
    const tempFiles: UploadedFileState[] = newFilesToUpload.map((file) => ({
      id: `${file.name}-${Date.now()}`,
      name: file.name,
      status: "uploading",
    }));

    setUploadedFiles((prev) => [...prev, ...tempFiles]);

    try {
      const result = await messageDocumentApi.uploadMessageDocuments(
        newFilesToUpload,
        currentOrganization.id
      );

      setUploadedFiles((prev) =>
        prev.map((file) => {
          const uploaded = result.data.find(
            (d: MessageDocument) => d.filename === file.name
          );
          if (uploaded) {
            return {
              ...uploaded,
              name: uploaded.filename,
              status: "completed",
            };
          }
          if (file.status === "uploading") {
            return { ...file, status: "error", error: "Upload failed" };
          }
          return file;
        })
      );
    } catch (error: any) {
      toast.error(error.message || "An unexpected error occurred.");
      setUploadedFiles((prev) =>
        prev.map((f) =>
          f.status === "uploading"
            ? { ...f, status: "error", error: "Upload failed" }
            : f
        )
      );
    } finally {
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleRemoveFile = async (fileId: string, status: string) => {
    if (status === "completed") {
      try {
        await messageDocumentApi.deleteMessageDocument(fileId);
      } catch (error) {
        console.error("Failed to delete document", error);
        toast.error("Failed to delete the uploaded file");
      }
    }
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!isStreaming && !isLoading && localValue.trim()) {
        handleSend();
      }
    }
  };

  const handleSend = () => {
    if (!isStreaming && !isLoading && localValue.trim()) {
      onSend(localValue.trim(), uploadedFiles, conversationVisibility);
      setLocalValue("");
      setUploadedFiles([]);
      // Reset visibility to private for next conversation
      setConversationVisibility("private");
    }
  };

  const handleStop = () => {
    if (isStreaming && onStop) {
      onStop();
    }
  };

  return (
    <div className="border-t bg-white dark:bg-gray-900 p-4">
      {/* Read-only message for non-owners */}
      {!isConversationOwner && !isNewConversation && (
        <div className="mb-4 p-3 bg-slate-100 dark:bg-slate-700 rounded-lg border border-slate-200 dark:border-slate-600">
          <div className="flex items-center gap-2 text-sm text-slate-600 dark:text-slate-400">
            <Lock className="w-4 h-4" />
            <span>
              This is a shared conversation. Only{" "}
              <span className="font-medium text-slate-900 dark:text-white">
                {conversationOwnerName || "the owner"}
              </span>{" "}
              can send messages.
            </span>
          </div>
        </div>
      )}

      {/* Speech Recognition Status */}
      {isListening && (
        <div className="flex items-center gap-2 mb-2 text-sm text-slate-600 dark:text-slate-400">
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span>Listening...</span>
          </div>
          {interimTranscript && (
            <span className="text-slate-500 italic">"{interimTranscript}"</span>
          )}
        </div>
      )}

      {/* Search Toggles and Visibility Controls */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-6">
          {/* File Search Toggle */}
          {/* <div className="flex items-center space-x-2">
            <Switch
              id="file-search"
              checked={fileSearchEnabled}
              onCheckedChange={onFileSearchToggle}
              disabled={disabled || isStreaming}
            />
            <Label
              htmlFor="file-search"
              className="text-sm flex items-center gap-2"
            >
              <FileText className="w-4 h-4" />
              Search documents
            </Label>
          </div> */}

          {/* Web Search Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="web-search"
              checked={webSearchEnabled}
              onCheckedChange={onWebSearchToggle}
              disabled={disabled || isStreaming}
            />
            <Label
              htmlFor="web-search"
              className="text-sm flex items-center gap-2"
            >
              <Globe className="w-4 h-4" />
              Include web search
            </Label>
          </div>
        </div>

        {/* Visibility Controls - only show for new conversations */}
        {showVisibilityControls && (
          <div className="flex items-center space-x-2">
            <Label className="text-sm text-slate-600 dark:text-slate-400">
              Visibility:
            </Label>
            <div className="flex items-center space-x-1 bg-slate-100 dark:bg-slate-800 rounded-md p-1">
              <Button
                variant={
                  conversationVisibility === "private" ? "default" : "ghost"
                }
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => setConversationVisibility("private")}
                disabled={disabled || isStreaming}
              >
                <Lock className="w-3 h-3 mr-1" />
                Private
              </Button>
              <Button
                variant={
                  conversationVisibility === "public" ? "default" : "ghost"
                }
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => setConversationVisibility("public")}
                disabled={disabled || isStreaming}
              >
                <Users className="w-3 h-3 mr-1" />
                Public
              </Button>
            </div>
          </div>
        )}
      </div>
      {/* Uploaded Files Preview */}
      {uploadedFiles.length > 0 && (
        <div className="mb-3 space-y-2">
          {uploadedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 p-2 rounded-md"
            >
              <div className="flex items-center gap-2">
                {file.status === "uploading" && (
                  <Loader2 className="w-4 h-4 animate-spin" />
                )}
                {file.status === "completed" && (
                  <FileText className="w-4 h-4 text-green-500" />
                )}
                {file.status === "error" && (
                  <FileText className="w-4 h-4 text-red-500" />
                )}
                <span className="text-sm">{file.name}</span>
                {file.status === "error" && (
                  <span className="text-sm text-red-500">- {file.error}</span>
                )}
              </div>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => handleRemoveFile(file.id, file.status)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
      {/* Input Area */}
      <div className="flex items-start gap-2">
        <div className="flex-1 relative">
          <Textarea
            value={
              localValue + (interimTranscript ? ` ${interimTranscript}` : "")
            }
            onChange={(e) => setLocalValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={
              isInputDisabled && !isConversationOwner
                ? "You can only view this conversation"
                : placeholder
            }
            disabled={isInputDisabled}
            className={`min-h-[60px] max-h-[200px] resize-none pr-24 ${
              isInputDisabled && !isConversationOwner
                ? "cursor-not-allowed opacity-60"
                : ""
            }`}
            rows={2}
          />
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            multiple
            accept="image/*,.pdf"
            className="hidden"
          />

          {/* Microphone Button */}
          {isSpeechSupported && (
            <Button
              variant="ghost"
              size="icon"
              className={`absolute right-12 top-1/2 -translate-y-1/2 ${
                isListening ? "text-red-500 animate-pulse" : "text-slate-500"
              }`}
              onClick={isListening ? stopListening : startListening}
              disabled={isInputDisabled || isStreaming}
              title={isListening ? "Stop dictation" : "Start dictation"}
            >
              {isListening ? (
                <MicOff className="w-5 h-5" />
              ) : (
                <Mic className="w-5 h-5" />
              )}
            </Button>
          )}

          {/* File Upload Button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 -translate-y-1/2"
            onClick={() => fileInputRef.current?.click()}
            disabled={isInputDisabled || isStreaming}
          >
            <Paperclip className="w-5 h-5" />
          </Button>
        </div>

        <div className="flex flex-col gap-2">
          {isStreaming ? (
            <Button
              onClick={handleStop}
              variant="outline"
              size="icon"
              className="h-[60px] w-12"
              disabled={!onStop}
            >
              <Square className="w-4 h-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSend}
              disabled={isInputDisabled || isLoading || !localValue.trim()}
              className="h-[60px] w-12"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
