"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, Users, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { ConversationAuthor } from "@/types/chat";

interface AuthorFilterProps {
  authors: ConversationAuthor[];
  selectedAuthorIds: string[];
  onAuthorSelect: (authorIds: string[]) => void;
  className?: string;
}

export function AuthorFilter({
  authors,
  selectedAuthorIds,
  onAuthorSelect,
  className,
}: AuthorFilterProps) {
  const [open, setOpen] = useState(false);

  const selectedAuthors = authors.filter((author) =>
    selectedAuthorIds.includes(author.id)
  );

  const handleAuthorToggle = (authorId: string) => {
    const newSelectedIds = selectedAuthorIds.includes(authorId)
      ? selectedAuthorIds.filter((id) => id !== authorId)
      : [...selectedAuthorIds, authorId];
    
    onAuthorSelect(newSelectedIds);
  };

  const handleClearAll = () => {
    onAuthorSelect([]);
  };

  const getAuthorDisplayName = (author: ConversationAuthor) => {
    return author.name || author.email;
  };

  const getAuthorInitials = (author: ConversationAuthor) => {
    const name = getAuthorDisplayName(author);
    return name.charAt(0).toUpperCase();
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-start text-left font-normal"
          >
            <Users className="mr-2 h-4 w-4 shrink-0" />
            {selectedAuthorIds.length === 0 ? (
              <span className="text-slate-500">Filter by authors...</span>
            ) : (
              <span>
                {selectedAuthorIds.length} author{selectedAuthorIds.length > 1 ? 's' : ''} selected
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <Command>
            <CommandInput placeholder="Search authors..." />
            <CommandList>
              <CommandEmpty>No authors found.</CommandEmpty>
              <CommandGroup>
                {authors.map((author) => (
                  <CommandItem
                    key={author.id}
                    value={getAuthorDisplayName(author)}
                    onSelect={() => handleAuthorToggle(author.id)}
                    className="flex items-center space-x-2"
                  >
                    <div className="flex items-center space-x-2 flex-1">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={author.avatar || undefined} />
                        <AvatarFallback className="text-xs">
                          {getAuthorInitials(author)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">
                          {author.name || "Unknown"}
                        </span>
                        <span className="text-xs text-slate-500">
                          {author.email}
                        </span>
                      </div>
                    </div>
                    <Check
                      className={cn(
                        "h-4 w-4",
                        selectedAuthorIds.includes(author.id)
                          ? "opacity-100"
                          : "opacity-0"
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
            {selectedAuthorIds.length > 0 && (
              <div className="border-t p-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  className="w-full text-slate-500 hover:text-slate-700"
                >
                  Clear all filters
                </Button>
              </div>
            )}
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected Authors Display */}
      {selectedAuthors.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selectedAuthors.map((author) => (
            <Badge
              key={author.id}
              variant="secondary"
              className="flex items-center gap-1 pr-1"
            >
              <Avatar className="w-4 h-4">
                <AvatarImage src={author.avatar || undefined} />
                <AvatarFallback className="text-xs">
                  {getAuthorInitials(author)}
                </AvatarFallback>
              </Avatar>
              <span className="text-xs">
                {author.name || author.email}
              </span>
              <Button
                variant="ghost"
                size="sm"
                className="h-4 w-4 p-0 hover:bg-slate-300"
                onClick={() => handleAuthorToggle(author.id)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
}
