"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Plus,
  Calendar,
  MoreVertical,
  Trash2,
  Loader2,
  Settings,
  Edit,
  Lock,
  Globe,
  Filter,
  ChevronDown,
  User,
  Share2,
} from "lucide-react";
import {
  Conversation,
  ConversationFilter,
  ConversationAuthor,
} from "@/types/chat";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AuthorFilter } from "./AuthorFilter";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ConversationsSidebarProps {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  isLoading: boolean;
  currentUserId?: string;
  filter?: ConversationFilter;
  authors?: ConversationAuthor[];
  selectedAuthorIds?: string[];
  onFilterChange?: (filter: ConversationFilter) => void;
  onAuthorSelect?: (authorIds: string[]) => void;
  onConversationSelect: (conversationId: string) => void;
  onCreateNew: () => void;
  onDeleteConversation: (conversationId: string) => void;
  onRenameConversation?: (conversationId: string, newTitle: string) => void;
  onUpdateVisibility?: (
    conversationId: string,
    visibility: "private" | "public"
  ) => void;
}

export function ConversationsSidebar({
  conversations,
  currentConversation,
  isLoading,
  currentUserId,
  filter,
  authors = [],
  selectedAuthorIds = [],
  onFilterChange,
  onAuthorSelect,
  onConversationSelect,
  onCreateNew,
  onDeleteConversation,
  onRenameConversation,
  onUpdateVisibility,
}: ConversationsSidebarProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingTitle, setEditingTitle] = useState("");

  // Helper functions
  const getVisibilityIcon = (visibility: "private" | "public") => {
    return visibility === "private" ? (
      <Lock className="w-3 h-3 text-slate-400" />
    ) : (
      <Globe className="w-3 h-3 text-green-500" />
    );
  };

  const getVisibilityLabel = (visibility: "private" | "public") => {
    return visibility === "private" ? "Private" : "Public";
  };

  const isOwner = (conversation: Conversation) => {
    return conversation.userId === currentUserId;
  };

  const getAuthorDisplayName = (conversation: Conversation) => {
    if (isOwner(conversation)) {
      return "You";
    }
    return (
      conversation.user?.name || conversation.user?.email || "Unknown User"
    );
  };

  return (
    <div className="w-full h-full bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-white">
            Houston
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onCreateNew}
              className="border-indigo-200 dark:border-indigo-800 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-950"
            >
              <Plus className="w-4 h-4 mr-1" />
              New Chat
            </Button>
          </div>
        </div>
        {/* Filter Dropdown */}
        {onFilterChange && (
          <div className="mb-3">
            <Select
              value={filter || "all"}
              onValueChange={(value) =>
                onFilterChange(value as ConversationFilter)
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Filter conversations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Chats</SelectItem>
                <SelectItem value="my-chats">My Chats</SelectItem>
                <SelectItem value="public-chats">Public Chats</SelectItem>
                <SelectItem value="by-author">By Author</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Author Selection - only show when "By Author" filter is selected */}
        {filter === "by-author" && onAuthorSelect && (
          <div className="mb-3">
            <AuthorFilter
              authors={authors}
              selectedAuthorIds={selectedAuthorIds}
              onAuthorSelect={onAuthorSelect}
            />
          </div>
        )}
      </div>

      {/* Conversations */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {isLoading && conversations.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-slate-400" />
            </div>
          ) : conversations.length === 0 ? (
            <div className="text-center py-8 text-slate-500 dark:text-slate-400">
              No conversations yet
            </div>
          ) : (
            conversations.map((conversation) => (
              <Card
                key={conversation.id}
                className={`mb-2 cursor-pointer transition-colors border group ${
                  currentConversation?.id === conversation.id
                    ? "bg-indigo-50 dark:bg-indigo-950 border-indigo-200 dark:border-indigo-800"
                    : "hover:bg-slate-50 dark:hover:bg-slate-700 border-transparent hover:border-slate-200 dark:hover:border-slate-600"
                }`}
                onClick={() => onConversationSelect(conversation.id)}
              >
                <CardContent className="p-3 max-w-[301px]">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      {editingId === conversation.id ? (
                        <input
                          className="font-medium text-slate-900 dark:text-white truncate text-sm bg-transparent border-b border-indigo-300 focus:outline-none focus:border-indigo-500 w-full"
                          value={editingTitle}
                          autoFocus
                          onChange={(e) => setEditingTitle(e.target.value)}
                          onBlur={() => {
                            if (
                              editingTitle.trim() &&
                              editingTitle !== conversation.title
                            ) {
                              onRenameConversation?.(
                                conversation.id,
                                editingTitle.trim()
                              );
                            }
                            setEditingId(null);
                          }}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              if (
                                editingTitle.trim() &&
                                editingTitle !== conversation.title
                              ) {
                                onRenameConversation?.(
                                  conversation.id,
                                  editingTitle.trim()
                                );
                              }
                              setEditingId(null);
                            } else if (e.key === "Escape") {
                              setEditingId(null);
                            }
                          }}
                        />
                      ) : (
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-slate-900 dark:text-white truncate text-sm cursor-pointer overflow-hidden text-ellipsis flex-1">
                            {conversation.title}
                          </h4>
                          <Badge
                            variant={
                              conversation.visibility === "private"
                                ? "secondary"
                                : "outline"
                            }
                            className="text-xs"
                          >
                            <span className="mr-2">
                              {getVisibilityIcon(conversation.visibility)}
                            </span>
                            <span>
                              {getVisibilityLabel(conversation.visibility)}
                            </span>
                          </Badge>
                        </div>
                      )}

                      {/* Author and Date */}
                      <div className="flex items-center justify-between mt-2 text-xs text-slate-400">
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          <span className="truncate max-w-[120px]">
                            {getAuthorDisplayName(conversation)}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Badge className="text-xs" variant="secondary">
                            <Calendar className="w-3 h-3 mr-1" />
                            {formatDate(conversation.updatedAt)}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {/* Only show edit options for conversation owner */}
                        {isOwner(conversation) && (
                          <>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                setEditingId(conversation.id);
                                setEditingTitle(conversation.title);
                              }}
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              Rename
                            </DropdownMenuItem>

                            {/* Visibility toggle */}
                            {onUpdateVisibility && (
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const newVisibility =
                                    conversation.visibility === "private"
                                      ? "public"
                                      : "private";
                                  onUpdateVisibility(
                                    conversation.id,
                                    newVisibility
                                  );
                                }}
                              >
                                {conversation.visibility === "private" ? (
                                  <>
                                    <Globe className="w-4 h-4 mr-2" />
                                    Make Public
                                  </>
                                ) : (
                                  <>
                                    <Lock className="w-4 h-4 mr-2" />
                                    Make Private
                                  </>
                                )}
                              </DropdownMenuItem>
                            )}

                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                onDeleteConversation(conversation.id);
                              }}
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </>
                        )}

                        {/* For non-owners, show limited options */}
                        {!isOwner(conversation) && (
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              // Could add "Report" or other options here
                            }}
                            disabled
                          >
                            <User className="w-4 h-4 mr-2" />
                            View Only
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
