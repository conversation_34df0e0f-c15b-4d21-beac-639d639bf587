import { useAuth } from "@/contexts/AuthContext";

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

// Generic API response type
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// Base API Client class
export class BaseApiClient {
  protected baseUrl: string;
  protected getAuthHeaders: () => Record<string, string>;

  constructor(baseUrl: string, getAuthHeaders: () => Record<string, string>) {
    this.baseUrl = baseUrl;
    this.getAuthHeaders = getAuthHeaders;
  }

  protected async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    let headers = {
      ...this.getAuthHeaders(),
      ...(options.headers as Record<string, string>),
    };

    // Only set Content-Type if not sending FormData
    if (!(options.body instanceof FormData)) {
      headers = {
        "Content-Type": "application/json",
        ...headers,
      };
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({
        message: `HTTP error! status: ${response.status}`,
      }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  public async post<T>(endpoint: string, body: any): Promise<T> {
    const isFormData = body instanceof FormData;
    return this.request<T>(endpoint, {
      method: "POST",
      body: isFormData ? body : JSON.stringify(body),
      headers: isFormData ? {} : { "Content-Type": "application/json" },
    });
  }

  public async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: "DELETE",
    });
  }
}

// Factory functions for different auth contexts
export const createBaseApiClient = (
  accessToken: string | null
): BaseApiClient => {
  console.log("Creating BaseApiClient with token:", {
    hasToken: !!accessToken,
    tokenPreview: accessToken ? `${accessToken.substring(0, 20)}...` : "null",
  });

  return new BaseApiClient(API_BASE_URL, () => {
    const headers = {
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    };

    console.log("Generated headers:", {
      hasAuth: !!headers.Authorization,
      authPreview: headers.Authorization
        ? `${headers.Authorization.substring(0, 30)}...`
        : "none",
    });

    return headers;
  });
};

// React hook for components using useAuth
export const useBaseApiClient = (): BaseApiClient => {
  const { accessToken } = useAuth();
  return createBaseApiClient(accessToken);
};

// Utility function for direct token access (for backward compatibility)
export const createApiClientWithStoredToken = (): BaseApiClient => {
  const token =
    typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;
  return createBaseApiClient(token);
};
