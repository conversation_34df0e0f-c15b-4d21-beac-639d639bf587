import { BaseApiClient, ApiResponse } from "./base";
import { useAuth } from "@/contexts/AuthContext";

// Organization types (matching the old api-client.ts structure)
export interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: string;
  createdAt: string;
}

export interface OrganizationsResponse {
  organizations: Organization[];
}

export interface OrganizationResponse {
  organization: Organization;
}

// Organization API Client
export class OrganizationApiClient extends BaseApiClient {
  async getOrganizations(): Promise<ApiResponse<Organization[]>> {
    return this.request("/api/organizations");
  }

  async createOrganization(data: {
    name: string;
    slug?: string;
  }): Promise<ApiResponse<Organization>> {
    return this.request("/api/organizations", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  async deleteOrganization(
    organizationId: string
  ): Promise<ApiResponse<{ success: boolean; message: string }>> {
    return this.request(`/api/organizations/${organizationId}`, {
      method: "DELETE",
    });
  }
}

// Factory functions
export const createOrganizationApiClient = (
  accessToken: string | null
): OrganizationApiClient => {
  return new OrganizationApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useOrganizationApi = (): OrganizationApiClient => {
  const { accessToken } = useAuth();
  return createOrganizationApiClient(accessToken);
};
