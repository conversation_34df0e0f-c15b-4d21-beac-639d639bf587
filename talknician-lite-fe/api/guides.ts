import { BaseApiClient } from "./base";
import { useAuth } from "@/contexts/AuthContext";
import { useMemo } from "react";

export interface Guide {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  status: "DRAFT" | "PROCESSING" | "REVIEW" | "PUBLISHED" | "ARCHIVED";
  visibility: "PRIVATE" | "ORGANIZATION" | "PUBLIC";
  videoUrl?: string;
  azureBlobUrl?: string;
  transcription?: string;
  summary?: string;
  estimatedDuration?: number;
  difficulty?: "beginner" | "intermediate" | "advanced";
  externalJobId?: string;
  externalJobStatus?: string;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  userId: string;
  organizationId: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  organization: {
    id: string;
    name: string;
    slug: string;
  };
  steps: GuideStep[];
  comments: GuideComment[];
}

export interface GuideStep {
  id: string;
  title: string;
  description?: string;
  stepNumber: number;
  startTime?: number;
  endTime?: number;
  stepSummary?: string;
  mediaId?: string;
  guideId: string;
  createdAt: string;
  updatedAt: string;
  media?: {
    id: string;
    type: "VIDEO" | "IMAGE";
    url: string;
  };
}

export interface GuideComment {
  id: string;
  content: string;
  isEdited: boolean;
  createdAt: string;
  updatedAt: string;
  userId: string;
  guideId: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
}

export interface CreateGuideRequest {
  title: string;
  description?: string;
  visibility?: "PRIVATE" | "ORGANIZATION" | "PUBLIC";
  videoFile: File;
  organizationId: string;
}

export interface UpdateGuideRequest {
  title?: string;
  description?: string;
  visibility?: "PRIVATE" | "ORGANIZATION" | "PUBLIC";
  status?: "DRAFT" | "REVIEW" | "PUBLISHED" | "ARCHIVED";
}

export interface GuidesResponse {
  guides: Guide[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface GuideFilters {
  status?: "DRAFT" | "PROCESSING" | "REVIEW" | "PUBLISHED" | "ARCHIVED";
  visibility?: "PRIVATE" | "ORGANIZATION" | "PUBLIC";
  search?: string;
  page?: number;
  limit?: number;
}

export class GuideApiClient extends BaseApiClient {
  /**
   * Get all guides with filtering and pagination
   */
  async getGuides(
    organizationId: string,
    filters: GuideFilters = {}
  ): Promise<GuidesResponse> {
    const params = new URLSearchParams();

    // Add organizationId as required parameter
    params.append("organizationId", organizationId);

    if (filters.status) params.append("status", filters.status);
    if (filters.visibility) params.append("visibility", filters.visibility);
    if (filters.search) params.append("search", filters.search);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());

    const response = await this.request<GuidesResponse>(
      `/api/guides?${params.toString()}`
    );
    return response;
  }

  /**
   * Get a specific guide by ID
   */
  async getGuide(guideId: string): Promise<Guide> {
    const response = await this.request<Guide>(`/api/guides/${guideId}`);
    return response;
  }

  /**
   * Create a new guide from video
   */
  async createGuideFromVideo(
    data: CreateGuideRequest
  ): Promise<{ guide: Guide }> {
    const formData = new FormData();
    formData.append("title", data.title);
    if (data.description) formData.append("description", data.description);
    if (data.visibility) formData.append("visibility", data.visibility);
    formData.append("organizationId", data.organizationId);
    formData.append("video", data.videoFile);

    const response = await this.request<{ guide: Guide }>(
      "/api/guides/upload",
      {
        method: "POST",
        body: formData,
      }
    );

    return response;
  }

  /**
   * Update an existing guide
   */
  async updateGuide(guideId: string, data: UpdateGuideRequest): Promise<Guide> {
    const response = await this.request(`/api/guides/${guideId}`, {
      method: "PATCH",
      body: JSON.stringify(data),
    });
    return response as Guide;
  }

  /**
   * Delete a guide
   */
  async deleteGuide(guideId: string): Promise<{ success: boolean }> {
    const response = await this.request(`/api/guides/${guideId}`, {
      method: "DELETE",
    });
    return response as { success: boolean };
  }

  /**
   * Complete guide processing (called after external job completion)
   */
  async completeGuideProcessing(
    guideId: string,
    status: string,
    srtUrl?: string
  ): Promise<{ success: boolean }> {
    const response = await this.request(`/api/guides/${guideId}/complete`, {
      method: "POST",
      body: JSON.stringify({ status, srtUrl }),
    });
    return response as { success: boolean };
  }

  /**
   * Add a comment to a guide
   */
  async addComment(guideId: string, content: string): Promise<GuideComment> {
    const response = await this.request(`/api/guides/${guideId}/comments`, {
      method: "POST",
      body: JSON.stringify({ content }),
    });
    return response as GuideComment;
  }

  /**
   * Update a comment
   */
  async updateComment(
    guideId: string,
    commentId: string,
    content: string
  ): Promise<GuideComment> {
    const response = await this.request(
      `/api/guides/${guideId}/comments/${commentId}`,
      {
        method: "PATCH",
        body: JSON.stringify({ content }),
      }
    );
    return response as GuideComment;
  }

  /**
   * Delete a comment
   */
  async deleteComment(
    guideId: string,
    commentId: string
  ): Promise<{ success: boolean }> {
    const response = await this.request(
      `/api/guides/${guideId}/comments/${commentId}`,
      {
        method: "DELETE",
      }
    );
    return response as { success: boolean };
  }

  /**
   * Get guide processing status (for polling if needed)
   */
  async getProcessingStatus(guideId: string): Promise<{
    status: string;
    progress?: number;
    message?: string;
    externalJobId?: string;
  }> {
    const response = await this.request(`/api/guides/${guideId}/status`);
    return response as {
      status: string;
      progress?: number;
      message?: string;
      externalJobId?: string;
    };
  }
}

// Factory functions
export const createGuideApiClient = (
  accessToken: string | null
): GuideApiClient => {
  return new GuideApiClient(
    process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
    () => ({
      "Content-Type": "application/json",
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
    })
  );
};

// React hook
export const useGuideApi = (): GuideApiClient => {
  const { accessToken } = useAuth();
  return useMemo(() => createGuideApiClient(accessToken), [accessToken]);
};
