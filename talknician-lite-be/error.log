{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:60:36\n\n  57 }\n  58 \n  59 // Get user from database\n→ 60 const user = await prisma.user.findUnique(\nThe table `public.User` does not exist in the current database.","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-03T12:16:18.132Z"}
{"error":"Request failed with status code 400","level":"error","message":"Failed to list files","service":"talknician-lite-api","timestamp":"2025-07-03T12:20:17.706Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Authorization":"Bearer EwBIBMl6BAAUBKgm8k1UswUNwklmy2v7U/S+1fEAAboFeEqDEQczMU9mwzUffnLc85A7QawiBN5ZOaBTsc8fY4P72ZdciqW7DSWmZO3rQK2LlIq0/+LzUV5oizNW+NE1hmTuq8AL5JpLHUk9PRsb3OYueiKAQJWGzzwZbzsntf2BMN+Oo10ltZZSq5BDySyf1h/UwDhd69CLQPDN3E4G2PI4pdTKFHJGRz8BZo0e3ShT+X0wy4ID4FXXJA+1ZBxIsodoK8g7gob/IMTPcaRIi3vls1pzCoqgNOUa/+2O7IwVxjbh2t4rv6wHEIy/KuwaibCD0UTpIWICceTa6wU5ohX5u1KCSRkXFPQkBc9wnMz5ZFHrBTr7v0BxyTPmzZcQZgAAEGc0W2hZClb+3gf6P9adjiYQA6LMykWR/9pVGxt5CQkE1FWbiA/wGJd3J3xeJndKy6pbkQuGWjrxdwnzWOBid4Vmm+D9UbkDCYqUj6uKYqWskxKEZe33K8AVJx0f0tde8rN4e3u2eNYoqnLd9jm8hpUWfm5oDnh7ugLUOZs5tR8i1i1MhSFv0GVI5QAJwfKHeRKarain21VJTV32+dwNLiHtsw8nXGznOn2Nf3CyH37lKXvTOGuAJz39U3GZAXW2SyAIlOR7cFp7J7QPi8YdbMlo2cj2DgmA7C+vHdmJLhV9GXoKjrDJM9QbnG59GtrlhyBcyrzZrs+Ax1Y7WWUSHhVo5KQGY6GGbtfDqAoc6r3MW0Iif+mhhAJ7bdsD5ZfRd9DwIq5pwMlbnnNanLxlPxmeSR6cfBi3QIz+12We4lyIY+7o41JqvjpLYSoOsQZzFBfwbsfuB1VIjwKfvxoS4NgOAv/F1NWuxJ8rckgfaWdd1gTlX7TpYC3S5zlN5Q28xb5VVSQqV2iMIIeSObyhSUOieQjJI1dAbvCGGQ9s0YLb7/7u18tcvboJcPj3Q/bhfDK9C4z5kT3QckwWrS6NB0Kj+88XOV+KHghWXkDmbqAtXpZer3wdiuH6+nWZuZ1xcBYkSEnxlfqkCsOE27TYeLIJkZWz2BMaTZDZ8xyBlcFCAmM5eINqnrLU3MU4h09HysHBt8bU0Q6GcypenGrEstqBiMWqGVylCkatWjqD0vCNrdg93QaJBWQnv2gcKxngXR//RU1fXGIlAIH9CNKKyFyv2GgzTbGT3RUq8u40E3BzOCqaN93pQiuhNdj3mOOOr5isjkMFtPqWnARqWpu40SifIDYzKc0HXn+3DPIZypCy8NSPrNsLSud0+LLwp/p1zixPjkAqMgwkzDinrHbwhZKdD7V0PZwK0BcPnLmNFvkFam9kbMbBWJlQ8tmQyISglIn+4JjXjbjYsvmN3RMq4Z+55+5/gHus9+98TbpPdfEwvnfth7M3eqjDD5iyfeNAaL15sHjbrSMvmnmBaG50sIfIzy3MO7gT9nBPOeoeUF75srdSAw==","User-Agent":"axios/1.10.0"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":0,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"https://graph.microsoft.com/v1.0/me/drive/root:3E11FFA25176E000!s7e0d58f7be77414eadaea850ab1b278c:/children","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 400","name":"AxiosError","stack":"AxiosError: Request failed with status code 400\n    at settle (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/settle.js:19:12)\n    at Unzip.handleStreamEnd (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/adapters/http.js:599:11)\n    at Unzip.emit (node:events:529:35)\n    at endReadableNT (node:internal/streams/readable:1368:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/axios@1.10.0/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/onedrive.routes.ts:292:22)","status":400},"level":"error","message":"Failed to list files","service":"talknician-lite-api","timestamp":"2025-07-03T13:16:33.434Z"}
{"auth0Id":"google-oauth2|107632961209860765890","error":{"error":"Bad Request","errorCode":"operation_not_supported","message":"The following user attributes cannot be updated: name. The connection (google-oauth2) must either be a database connection (using the Auth0 store), a passwordless connection (email or sms) or has disabled 'Sync user profile attributes at each login'. For more information, see https://auth0.com/docs/dashboard/guides/connections/configure-connection-sync","statusCode":400},"level":"error","message":"Failed to update Auth0 user profile","service":"talknician-lite-api","timestamp":"2025-07-03T13:31:37.240Z"}
{"auth0Id":"google-oauth2|107632961209860765890","error":{"error":"Bad Request","errorCode":"operation_not_supported","message":"The following user attributes cannot be updated: name. The connection (google-oauth2) must either be a database connection (using the Auth0 store), a passwordless connection (email or sms) or has disabled 'Sync user profile attributes at each login'. For more information, see https://auth0.com/docs/dashboard/guides/connections/configure-connection-sync","statusCode":400},"level":"error","message":"Failed to update Auth0 user profile","service":"talknician-lite-api","timestamp":"2025-07-03T13:31:46.332Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:60:36\n\n  57 }\n  58 \n  59 // Get user from database\n→ 60 const user = await prisma.user.findUnique(\nServer has closed the connection.","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-07T01:04:15.726Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:60:36\n\n  57 }\n  58 \n  59 // Get user from database\n→ 60 const user = await prisma.user.findUnique(\nServer has closed the connection.","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-07T01:04:15.789Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:60:36\n\n  57 }\n  58 \n  59 // Get user from database\n→ 60 const user = await prisma.user.findUnique(\nCan't reach database server at `talknician-lite-southeast-asia.postgres.database.azure.com:5432`\n\nPlease make sure your database server is running at `talknician-lite-southeast-asia.postgres.database.azure.com:5432`.","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-07T01:21:49.668Z"}
{"error":"Unexpected token '-', \"------WebK\"... is not valid JSON","level":"error","message":"Unhandled error","method":"POST","service":"talknician-lite-api","stack":"SyntaxError: Unexpected token '-', \"------WebK\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/types/json.js:169:10)\n    at parse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/types/json.js:86:15)\n    at /Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:520:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)","timestamp":"2025-07-07T02:20:31.514Z","url":"/api/message-documents/upload"}
{"error":"Unexpected token '-', \"------WebK\"... is not valid JSON","level":"error","message":"Unhandled error","method":"POST","service":"talknician-lite-api","stack":"SyntaxError: Unexpected token '-', \"------WebK\"... is not valid JSON\n    at JSON.parse (<anonymous>)\n    at createStrictSyntaxError (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/types/json.js:169:10)\n    at parse (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/types/json.js:86:15)\n    at /Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/lib/read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at invokeCallback (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:238:16)\n    at done (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:227:7)\n    at IncomingMessage.onEnd (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js:287:7)\n    at IncomingMessage.emit (node:events:520:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)","timestamp":"2025-07-07T02:24:08.711Z","url":"/api/message-documents/upload"}
{"level":"error","message":"Error uploading document: Cannot destructure property 'organizationId' of 'req.body' as it is undefined.","service":"talknician-lite-api","stack":"TypeError: Cannot destructure property 'organizationId' of 'req.body' as it is undefined.\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:65:15)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at multerMiddleware (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/multer@1.4.5-lts.2/node_modules/multer/lib/make-middleware.js:13:41)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:101:5)","timestamp":"2025-07-07T02:25:31.546Z"}
{"level":"error","message":"Error uploading document: Cannot destructure property 'organizationId' of 'req.body' as it is undefined.","service":"talknician-lite-api","stack":"TypeError: Cannot destructure property 'organizationId' of 'req.body' as it is undefined.\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:65:15)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at multerMiddleware (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/multer@1.4.5-lts.2/node_modules/multer/lib/make-middleware.js:13:41)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:101:5)","timestamp":"2025-07-07T02:25:49.444Z"}
{"level":"error","message":"Error uploading document: Cannot read properties of undefined (reading 'user')","service":"talknician-lite-api","stack":"TypeError: Cannot read properties of undefined (reading 'user')\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:65:39)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at multerMiddleware (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/multer@1.4.5-lts.2/node_modules/multer/lib/make-middleware.js:13:41)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:101:5)","timestamp":"2025-07-07T02:27:50.249Z"}
{"code":"ENOENT","errno":-2,"level":"error","message":"Error uploading document: ENOENT: no such file or directory, unlink 'temp/1751855761619-228135977.webp'","path":"temp/1751855761619-228135977.webp","service":"talknician-lite-api","stack":"Error: ENOENT: no such file or directory, unlink 'temp/1751855761619-228135977.webp'\n    at Object.unlinkSync (node:fs:1870:11)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:143:12)","syscall":"unlink","timestamp":"2025-07-07T02:36:05.761Z"}
{"code":"ENOENT","errno":-2,"level":"error","message":"Error uploading document: ENOENT: no such file or directory, unlink 'temp/1751856164501-876424706.webp'","path":"temp/1751856164501-876424706.webp","service":"talknician-lite-api","stack":"Error: ENOENT: no such file or directory, unlink 'temp/1751856164501-876424706.webp'\n    at Object.unlinkSync (node:fs:1870:11)\n    at <anonymous> (/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/routes/message-document.routes.ts:143:12)","syscall":"unlink","timestamp":"2025-07-07T02:42:49.186Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:09.406Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:09.407Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:09.408Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:53.151Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:53.152Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:27:53.153Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:31:32.284Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:31:32.284Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:31:32.285Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:32:26.794Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:32:26.795Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:33:05.767Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:33:05.768Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:33:38.147Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:33:38.149Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:33:38.149Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:35:39.308Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:35:39.309Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:35:50.671Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:35:50.672Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:37:26.130Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:37:26.131Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:37:26.131Z"}
{"error":"Request failed with status code 401","level":"error","message":"Failed to get Management API token","service":"talknician-lite-api","timestamp":"2025-07-07T15:39:20.318Z"}
{"email":"<EMAIL>","error":"Failed to authenticate with Auth0 Management API","level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:39:20.318Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:39:20.319Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:39:38.666Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:39:38.667Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:41:28.443Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:41:28.444Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:03.245Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:03.246Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:15.512Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:15.513Z"}
{"email":"<EMAIL>","error":{"attributes":{"error":"Invalid token"},"error":"Unauthorized","message":"Invalid token","statusCode":401},"level":"error","message":"Auth0 signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:33.563Z"}
{"email":"<EMAIL>","error":"Signup failed. Please try again.","level":"error","message":"Signup failed","service":"talknician-lite-api","timestamp":"2025-07-07T15:44:33.564Z"}
{"auth0Id":"google-oauth2|107632961209860765890","error":{"error":"Bad Request","errorCode":"operation_not_supported","message":"The following user attributes cannot be updated: name. The connection (google-oauth2) must either be a database connection (using the Auth0 store), a passwordless connection (email or sms) or has disabled 'Sync user profile attributes at each login'. For more information, see https://auth0.com/docs/dashboard/guides/connections/configure-connection-sync","statusCode":400},"level":"error","message":"Failed to update Auth0 user profile","service":"talknician-lite-api","timestamp":"2025-07-15T13:28:04.493Z"}
{"auth0Id":"google-oauth2|107632961209860765890","error":{"error":"Bad Request","errorCode":"operation_not_supported","message":"The following user attributes cannot be updated: name. The connection (google-oauth2) must either be a database connection (using the Auth0 store), a passwordless connection (email or sms) or has disabled 'Sync user profile attributes at each login'. For more information, see https://auth0.com/docs/dashboard/guides/connections/configure-connection-sync","statusCode":400},"level":"error","message":"Failed to update Auth0 user profile","service":"talknician-lite-api","timestamp":"2025-07-15T13:28:14.554Z"}
{"auth0Id":"google-oauth2|107632961209860765890","error":{"error":"Bad Request","errorCode":"operation_not_supported","message":"The following user attributes cannot be updated: name. The connection (google-oauth2) must either be a database connection (using the Auth0 store), a passwordless connection (email or sms) or has disabled 'Sync user profile attributes at each login'. For more information, see https://auth0.com/docs/dashboard/guides/connections/configure-connection-sync","statusCode":400},"level":"error","message":"Failed to update Auth0 user profile","service":"talknician-lite-api","timestamp":"2025-07-15T14:08:40.106Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:62:36\n\n  59 }\n  60 \n  61 // Get user from database\n→ 62 const user = await prisma.user.findUnique(\nCan't reach database server at `talknician-lite-southeast-asia.postgres.database.azure.com:5432`\n\nPlease make sure your database server is running at `talknician-lite-southeast-asia.postgres.database.azure.com:5432`.","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-15T23:12:25.601Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-07-20T21:47:34.587Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-07-20T21:47:34.587Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T21:47:34.588Z"}
{"error":"\nInvalid `prisma.user.findUnique()` invocation in\n/Users/<USER>/vscode/talknician-lite/talknician-lite-be/src/middleware/auth.middleware.ts:62:36\n\n  59 }\n  60 \n  61 // Get user from database\n→ 62 const user = await prisma.user.findUnique(\nTimed out fetching a new connection from the connection pool. More info: http://pris.ly/d/connection-pool (Current connection pool timeout: 10, connection limit: 17)","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T21:55:50.398Z"}
{"hasDecoded":false,"hasKid":false,"level":"error","message":"Token decode failed","service":"talknician-lite-api","timestamp":"2025-07-20T22:31:17.623Z"}
{"error":"Invalid token","errorName":"Error","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-07-20T22:31:17.623Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T22:31:17.623Z"}
{"error":"jwt expired","errorName":"TokenExpiredError","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-07-20T22:33:29.067Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T22:33:29.068Z"}
{"error":"invalid signature","errorName":"JsonWebTokenError","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-07-20T22:38:07.291Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T22:38:07.291Z"}
{"error":"invalid signature","errorName":"JsonWebTokenError","level":"error","message":"Token verification failed","service":"talknician-lite-api","timestamp":"2025-07-20T22:44:20.060Z"}
{"error":"Token verification failed","level":"error","message":"Authentication middleware error","service":"talknician-lite-api","timestamp":"2025-07-20T22:44:20.061Z"}
