# Backend Server .env.template
# Copy this file to .env and fill in the values.
PORT=8000

# Database
DATABASE_URL=

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_MAXMEMORY_POLICY=noeviction

# Auth0 Configuration
AUTH0_DOMAIN=
AUTH0_CLIENT_ID=
AUTH0_CLIENT_SECRET=
AUTH0_API_AUDIENCE=
AUTH0_API_IDENTIFIER=
AUTH0_M2M_CLIENT_ID=
AUTH0_M2M_CLIENT_SECRET=

# OpenAI API Key
OPENAI_API_KEY=

# AZURE STORAGE
AZURE_STORAGE_ACCOUNT_NAME=
AZURE_STORAGE_ACCOUNT_KEY=
AZURE_STORAGE_CONTAINER_NAME=talknician-uploads
AZURE_STORAGE_CONNECTION_STRING=

# Crawler Service URL for the official crawl4ai docker image
CRAWL4AI_SERVICE_URL="http://localhost:8080/v1/crawl"

# Rate Limiting
# See: https://www.npmjs.com/package/express-rate-limit
# Default: 100 requests per 15 minutes
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Pusher for real-time notifications
PUSHER_APP_ID="*******"
PUSHER_KEY="fc993c1372675a3637b2"
PUSHER_SECRET="fe720efbef34cd200baa"
PUSHER_CLUSTER="ap1"