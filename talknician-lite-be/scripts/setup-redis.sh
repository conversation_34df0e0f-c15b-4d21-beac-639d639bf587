#!/bin/bash

# Redis Setup Script for Talknician Li<PERSON>
# This script helps set up Redis with the correct eviction policy

set -e

echo "🔧 Setting up Redis for Talknician Lite..."
echo ""

# Check if Redis is running
if command -v redis-cli &> /dev/null; then
    echo "✅ Redis CLI found"
    
    # Check if Redis server is running
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis server is running"
        
        # Check current eviction policy
        CURRENT_POLICY=$(redis-cli config get maxmemory-policy | tail -n 1)
        echo "📊 Current eviction policy: $CURRENT_POLICY"
        
        if [ "$CURRENT_POLICY" = "noeviction" ]; then
            echo "✅ Redis eviction policy is already set to 'noeviction'"
        else
            echo "⚠️  Current policy is '$CURRENT_POLICY', setting to 'noeviction'..."
            redis-cli config set maxmemory-policy noeviction
            echo "✅ Redis eviction policy set to 'noeviction'"
        fi
        
        # Verify the change
        NEW_POLICY=$(redis-cli config get maxmemory-policy | tail -n 1)
        echo "📊 New eviction policy: $NEW_POLICY"
        
    else
        echo "❌ Redis server is not running"
        echo ""
        echo "To start Redis:"
        echo "  - On macOS: brew services start redis"
        echo "  - On Ubuntu: sudo systemctl start redis-server"
        echo "  - On Windows: Start Redis service"
        echo "  - Or use Docker: docker run -d -p 6379:6379 redis:7-alpine redis-server --maxmemory-policy noeviction"
    fi
else
    echo "❌ Redis CLI not found"
    echo ""
    echo "To install Redis:"
    echo "  - On macOS: brew install redis"
    echo "  - On Ubuntu: sudo apt-get install redis-server"
    echo "  - On Windows: Download from https://redis.io/download"
    echo "  - Or use Docker: docker run -d -p 6379:6379 redis:7-alpine redis-server --maxmemory-policy noeviction"
fi

echo ""
echo "📝 Manual Redis Configuration:"
echo "If you're running Redis manually, add this to your redis.conf:"
echo "  maxmemory-policy noeviction"
echo ""
echo "Or start Redis with:"
echo "  redis-server --maxmemory-policy noeviction"
echo ""
echo "🎉 Redis setup complete!" 