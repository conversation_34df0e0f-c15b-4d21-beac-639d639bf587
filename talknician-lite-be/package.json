{"name": "talknician-lite-be", "version": "1.0.7", "description": "Talknician Lite Backend API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && tsc-alias", "start": "node dist/index.js", "lint": "eslint . --ext .ts --fix", "lint:check": "eslint . --ext .ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "redis:setup": "./scripts/setup-redis.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "preversion": "pnpm lint && pnpm build", "version": "echo Version bumped to $npm_package_version", "postversion": "git push && git push --tags", "release": "git add . && git commit -m 'chore: pre-version commit' || echo 'No changes to commit' && npm version patch -m '%s' && git push && git push --tags"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "@prisma/client": "^5.7.1", "@types/express-session": "^1.18.2", "@types/express-sse": "^0.5.1", "@types/ioredis": "^5.0.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bullmq": "^5.56.5", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-jwt": "^8.4.1", "express-rate-limit": "^7.1.5", "express-session": "^1.18.1", "express-sse": "^1.0.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.3", "openai": "^4.20.1", "prisma": "^5.7.1", "pusher": "^5.2.0", "redis": "^5.6.0", "uuid": "^9.0.1", "version": "^0.1.2", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.30.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "jest": "^30.0.2", "nodemon": "^3.0.2", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "tsc-alias": "^1.8.8", "tsx": "^4.6.2", "typescript": "^5.3.3", "typescript-eslint": "^8.35.0"}, "keywords": ["express", "typescript", "auth0", "prisma", "postgresql", "api"], "author": "Talknician Team", "license": "MIT"}