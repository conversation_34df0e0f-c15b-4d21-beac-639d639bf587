# Redis Setup Guide for Talknician Lite

## 🚨 Important: Redis Eviction Policy

**The Redis eviction policy MUST be set to `noeviction` to prevent data loss.** The default `volatile-lru` policy can cause job queue data to be evicted, leading to lost video processing jobs and guide generation tasks.

## 🔧 Quick Setup

### Option 1: Automated Setup (Recommended)

Run the setup script to automatically configure Redis:

```bash
pnpm redis:setup
```

### Option 2: Docker Compose (Recommended for Development)

Use the provided Docker Compose setup which includes Redis with the correct configuration:

```bash
# Start all services (PostgreSQL, Redis, Backend)
pnpm docker:up

# View logs
pnpm docker:logs

# Stop services
pnpm docker:down
```

### Option 3: Manual Setup

#### Install Redis

**macOS:**

```bash
brew install redis
```

**Ubuntu/Debian:**

```bash
sudo apt-get update
sudo apt-get install redis-server
```

**Windows:**
Download from [Redis Downloads](https://redis.io/download)

#### Configure Redis

1. **Using the provided config file:**

   ```bash
   redis-server ./redis.conf
   ```

2. **Or set the policy manually:**

   ```bash
   # Start Redis
   redis-server

   # In another terminal, set the eviction policy
   redis-cli config set maxmemory-policy noeviction
   ```

3. **Or start Redis with the policy:**
   ```bash
   redis-server --maxmemory-policy noeviction
   ```

## 📋 Environment Configuration

Add these variables to your `.env` file:

```env
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_MAXMEMORY_POLICY=noeviction
```

## 🔍 Verification

Check that Redis is configured correctly:

```bash
# Test connection
redis-cli ping

# Check eviction policy
redis-cli config get maxmemory-policy
```

You should see:

```
PONG
1) "maxmemory-policy"
2) "noeviction"
```

## 🐳 Docker Setup

The `docker-compose.yml` file includes:

- **Redis**: Cache and job queue with correct eviction policy
- **Backend**: Node.js API server
- **PostgreSQL**: Hosted externally (configure via DATABASE_URL in .env)

### Services

```yaml
services:
  redis:
    image: redis:7-alpine
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    # Redis with noeviction policy...

  backend:
    build: .
    depends_on:
      - redis
    # Backend API with external PostgreSQL connection...
```

## 🚨 Troubleshooting

### "Eviction policy is volatile-lru" Warning

If you see this warning, Redis is using the default eviction policy which can cause data loss.

**Solution:**

```bash
# Connect to Redis
redis-cli

# Set the correct policy
config set maxmemory-policy noeviction

# Verify the change
config get maxmemory-policy
```

### Redis Connection Issues

1. **Check if Redis is running:**

   ```bash
   redis-cli ping
   ```

2. **Check Redis logs:**

   ```bash
   # If using system Redis
   sudo journalctl -u redis-server

   # If using Docker
   docker-compose logs redis
   ```

3. **Check port availability:**
   ```bash
   netstat -an | grep 6379
   ```

### Performance Considerations

- **Memory Usage**: With `noeviction` policy, Redis will not evict data. Monitor memory usage and ensure sufficient RAM.
- **Persistence**: The configuration includes AOF (Append Only File) for data persistence.
- **Backup**: Regular Redis snapshots are configured for data backup.

## 📊 Monitoring

### Health Checks

The application includes health checks for Redis:

```bash
# Check application health
curl http://localhost:8000/health

# Check Redis directly
redis-cli ping
```

### Logs

Monitor Redis logs for issues:

```bash
# Docker logs
docker-compose logs -f redis

# System logs
sudo journalctl -u redis-server -f
```

## 🔒 Security

For production environments:

1. **Set a password:**

   ```bash
   redis-cli config set requirepass your_strong_password
   ```

2. **Update environment variables:**

   ```env
   REDIS_PASSWORD=your_strong_password
   ```

3. **Bind to localhost only:**
   ```bash
   redis-cli config set bind 127.0.0.1
   ```

## 📚 Additional Resources

- [Redis Configuration](https://redis.io/topics/config)
- [Redis Eviction Policies](https://redis.io/topics/lru-cache)
- [BullMQ Documentation](https://docs.bullmq.io/)
- [Docker Redis](https://hub.docker.com/_/redis)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Verify Redis configuration with `redis-cli config get maxmemory-policy`
3. Check application logs for Redis connection errors
4. Ensure Redis is running and accessible on the configured host/port
