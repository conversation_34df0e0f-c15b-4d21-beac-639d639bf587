version: "3.8"

services:
  # Redis with correct eviction policy
  redis:
    image: redis:7-alpine
    container_name: talknician-redis
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build: .
    container_name: talknician-backend
    environment:
      - NODE_ENV=development
      # Use external PostgreSQL server - update DATABASE_URL in your .env file
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_MAXMEMORY_POLICY=noeviction
      # Add other environment variables as needed
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
