import { prisma } from "@/config/database";
import { azureStorageService } from "./azure-storage.service";
import { openai } from "./openai.service";
import { logger } from "@/utils/logger";
import axios from "axios";
import { videoProcessingEventService } from "./video-processing.service";
import { queueService } from "./queue.service";
import {
  aiGuideGeneratorService,
  type StructuredGuideResponse,
  type GuideStepData as AIGuideStepData,
} from "./ai-guide-generator.service";
import { Prisma, $Enums } from "@prisma/client";

// Use Prisma-generated enums
type GuideStatus = $Enums.GuideStatus;
type GuideVisibility = $Enums.GuideVisibility;
type MediaType = $Enums.MediaType;

const GuideStatus = $Enums.GuideStatus;
const GuideVisibility = $Enums.GuideVisibility;
const MediaType = $Enums.MediaType;

interface CreateGuideFromVideoOptions {
  title: string;
  description?: string;
  videoFile: Express.Multer.File;
  userId: string;
  organizationId: string;
  visibility?: GuideVisibility;
}

interface GuideStepData {
  title: string;
  description: string;
  startTime?: number;
  endTime?: number;
  stepSummary?: string;
  keyPoints?: string[];
  actionItems?: string[];
}

interface StructuredGuideResponse {
  summary: string;
  overview: string;
  estimatedDuration: number;
  difficulty: "beginner" | "intermediate" | "advanced";
  prerequisites: string[];
  steps: GuideStepData[];
  keyTakeaways: string[];
  troubleshooting?: {
    issue: string;
    solution: string;
  }[];
}

class GuideService {
  private readonly VIDEO_TO_SRT_API_URL =
    process.env.VIDEO_TO_SRT_API_URL || "http://localhost:8000";

  /**
   * Create a guide from video file
   */
  async createGuideFromVideo(options: CreateGuideFromVideoOptions) {
    const {
      title,
      description,
      videoFile,
      userId,
      organizationId,
      visibility = GuideVisibility.PRIVATE,
    } = options;

    try {
      // Upload video to Azure Storage
      const azureBlobUrl = await azureStorageService.uploadFileFromBuffer(
        videoFile.buffer,
        videoFile.originalname,
        videoFile.mimetype,
        organizationId
      );

      // Create initial guide in PROCESSING status
      const guide = await prisma.guide.create({
        data: {
          title,
          description,
          status: GuideStatus.PROCESSING,
          visibility,
          videoUrl: azureBlobUrl,
          azureBlobUrl,
          userId,
          organizationId,
        },
      });

      // Add video processing job to queue for better performance and reliability
      const job = await queueService.addVideoProcessingJob({
        guideId: guide.id,
        userId,
        organizationId,
        videoUrl: azureBlobUrl,
      });

      // Update guide with queue job info
      const updatedGuide = await prisma.guide.update({
        where: { id: guide.id },
        data: {
          externalJobId: job.id?.toString(),
          externalJobStatus: "QUEUED",
        },
      });

      logger.info("Guide creation initiated", {
        guideId: guide.id,
        jobId: job.id,
        userId,
      });

      // Send initial status update via Pusher
      videoProcessingEventService.sendStatusUpdate({
        guideId: guide.id,
        userId,
        organizationId,
        status: "PENDING",
        progress: 10,
        message: "Video processing job queued",
        externalJobId: job.id?.toString(),
      });

      // Return the guide with job info; client will receive real-time updates
      return { guide: updatedGuide };
    } catch (error) {
      logger.error("Error creating guide from video:", error);
      throw new Error("Failed to create guide from video");
    }
  }

  /**
   * Update guide after client-side polling is complete
   */
  async completeGuideProcessing(
    guideId: string,
    jobStatus: string,
    srtUrl?: string
  ) {
    try {
      // Get guide info for user notifications
      const guide = await prisma.guide.findUnique({
        where: { id: guideId },
        select: { userId: true, organizationId: true },
      });

      if (!guide) {
        throw new Error("Guide not found");
      }

      // If job failed, mark as draft and send error notification
      if (jobStatus === "FAILED") {
        await prisma.guide.update({
          where: { id: guideId },
          data: {
            status: GuideStatus.DRAFT,
            externalJobStatus: jobStatus,
          },
        });

        videoProcessingEventService.sendProcessingError(
          guideId,
          guide.userId,
          guide.organizationId,
          "Video processing failed"
        );
        return;
      }
      // If job completed, fetch transcription and generate summary/steps
      if (jobStatus === "COMPLETED" && srtUrl) {
        // Send status update: Starting AI processing
        videoProcessingEventService.sendProgressUpdate(
          guideId,
          guide.userId,
          guide.organizationId,
          "GENERATING_SUMMARY",
          70,
          "Generating guide summary and steps..."
        );

        const transcriptionResponse = await axios.get(srtUrl);
        const transcription = transcriptionResponse.data;
        let summary = "";
        let steps: GuideStepData[] = [];

        if (transcription && transcription.trim() !== "") {
          // Generate enhanced summary
          summary = await aiGuideGeneratorService.generateEnhancedSummary(
            transcription
          );

          // Update progress after summary generation
          videoProcessingEventService.sendProgressUpdate(
            guideId,
            guide.userId,
            guide.organizationId,
            "GENERATING_STEPS",
            85,
            "Generating structured guide with AI..."
          );

          // Generate structured guide with timestamps
          const structuredGuide =
            await aiGuideGeneratorService.generateStructuredGuide(
              transcription
            );
          steps = structuredGuide.steps.map((step) => ({
            title: step.title,
            description: step.description,
            startTime: step.startTime,
            endTime: step.endTime,
            stepSummary: step.stepSummary,
          }));
        } else {
          summary =
            "This video contains no audio content. Please add steps manually.";
        }
        // Update guide
        await prisma.guide.update({
          where: { id: guideId },
          data: {
            status: GuideStatus.DRAFT,
            externalJobStatus: jobStatus,
            transcription,
            summary,
          },
        });
        // Create steps if transcription exists
        if (steps.length > 0) {
          const updatedGuide = await prisma.guide.findUnique({
            where: { id: guideId },
          });
          if (updatedGuide) {
            await this.createGuideSteps(
              guideId,
              steps,
              updatedGuide.videoUrl ?? ""
            );
          }
        }

        // Send final completion notification
        videoProcessingEventService.sendProgressUpdate(
          guideId,
          guide.userId,
          guide.organizationId,
          "COMPLETED",
          100,
          "Guide processing completed successfully!"
        );

        return;
      }
      // Otherwise, just update the status
      await prisma.guide.update({
        where: { id: guideId },
        data: {
          externalJobStatus: jobStatus,
        },
      });
    } catch (error) {
      logger.error("Error updating guide after client polling:", error);
      throw error;
    }
  }

  /**
   * Handle video with no audio
   */
  private async handleNoAudioVideo(guideId: string, videoUrl: string) {
    try {
      await prisma.guide.update({
        where: { id: guideId },
        data: {
          status: GuideStatus.DRAFT,
          transcription: "",
          summary:
            "This video contains no audio content. Please add steps manually.",
        },
      });

      // Create a Media record for the video
      const media = await prisma.media.create({
        data: {
          type: MediaType.VIDEO,
          url: videoUrl,
        },
      });

      // Create a single step for the full video
      await prisma.guideStep.create({
        data: {
          title: "Video Step",
          description:
            "This video contains no audio. Please add description manually.",
          stepNumber: 1,
          mediaId: media.id,
          guideId,
        },
      });

      logger.info("Handled no-audio video", { guideId });
    } catch (error) {
      logger.error("Error handling no-audio video:", error);
      throw error;
    }
  }

  /**
   * Generate summary using OpenAI
   */
  private async generateSummary(transcription: string): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a helpful assistant that creates concise summaries of video transcriptions for instructional guides.",
          },
          {
            role: "user",
            content: `Please create a brief summary (2-3 sentences) of this video transcription:\n\n${transcription}`,
          },
        ],
        max_tokens: 150,
        temperature: 0.7,
      });
      return response.choices[0]?.message?.content || "Summary not available";
    } catch (error) {
      logger.error("Error generating summary:", error);
      return "Summary generation failed";
    }
  }

  /**
   * Generate guide steps using OpenAI
   */
  private async generateGuideSteps(
    transcription: string
  ): Promise<GuideStepData[]> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `You are a helpful assistant that creates step-by-step guides from video transcriptions. 
            Break down the content into clear, actionable steps. Each step should have a title and description.
            Return the response as a JSON array with objects containing 'title' and 'description' fields.`,
          },
          {
            role: "user",
            content: `Please create step-by-step instructions from this video transcription:\n\n${transcription}`,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      });
      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error("No content generated");
      }
      // Try to parse JSON response
      try {
        const steps = JSON.parse(content);
        if (Array.isArray(steps)) {
          return steps.map((step: GuideStepData) => ({
            title: step.title || "Step",
            description: step.description || "",
          }));
        }
      } catch (parseError) {
        logger.warn(
          "Failed to parse OpenAI response as JSON, creating fallback steps"
        );
      }
      // Fallback: create a single step
      return [
        {
          title: "Guide Step",
          description: content,
        },
      ];
    } catch (error) {
      logger.error("Error generating guide steps:", error);
      return [
        {
          title: "Manual Step Required",
          description: "Please add steps manually as auto-generation failed.",
        },
      ];
    }
  }

  /**
   * Create guide steps in database
   */
  private async createGuideSteps(
    guideId: string,
    stepsData: GuideStepData[],
    videoUrl: string
  ) {
    try {
      for (let i = 0; i < stepsData.length; i++) {
        const stepData = stepsData[i];
        // Create a Media record for the video for each step
        const media = await prisma.media.create({
          data: {
            type: MediaType.VIDEO,
            url: videoUrl,
          },
        });
        await prisma.guideStep.create({
          data: {
            title: stepData.title,
            description: stepData.description,
            stepNumber: i + 1,
            mediaId: media.id,
            stepSummary: stepData.stepSummary,
            guideId,
          },
        });
      }
      logger.info("Guide steps created", {
        guideId,
        stepCount: stepsData.length,
      });
    } catch (error) {
      logger.error("Error creating guide steps:", error);
      throw error;
    }
  }

  /**
   * Get guide by ID with full details
   */
  async getGuideById(guideId: string, userId: string) {
    try {
      const guide = await prisma.guide.findUnique({
        where: { id: guideId },
        include: {
          steps: {
            orderBy: { stepNumber: "asc" },
            include: { media: true },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          comments: true,
        },
      });
      if (!guide) {
        throw new Error("Guide not found");
      }
      // Check visibility permissions
      if (
        guide.visibility === GuideVisibility.PRIVATE &&
        guide.userId !== userId
      ) {
        throw new Error("Access denied");
      }
      return guide;
    } catch (error) {
      logger.error("Error getting guide by ID:", error);
      throw error;
    }
  }

  /**
   * Update guide
   */
  async updateGuide(
    guideId: string,
    userId: string,
    updateData: {
      title?: string;
      description?: string;
      visibility?: GuideVisibility;
      status?: GuideStatus;
    }
  ) {
    try {
      // Check ownership
      const guide = await prisma.guide.findUnique({
        where: { id: guideId },
      });
      if (!guide || guide.userId !== userId) {
        throw new Error("Guide not found or access denied");
      }
      const updatedGuide = await prisma.guide.update({
        where: { id: guideId },
        data: {
          ...updateData,
          publishedAt:
            updateData.status === GuideStatus.PUBLISHED
              ? new Date()
              : undefined,
        },
        include: {
          steps: {
            orderBy: { stepNumber: "asc" },
            include: { media: true },
          },
        },
      });
      logger.info("Guide updated", { guideId, userId });
      return updatedGuide;
    } catch (error) {
      logger.error("Error updating guide:", error);
      throw error;
    }
  }

  /**
   * Delete guide
   */
  async deleteGuide(guideId: string, userId: string) {
    try {
      const guide = await prisma.guide.findUnique({
        where: { id: guideId },
      });
      if (!guide || guide.userId !== userId) {
        throw new Error("Guide not found or access denied");
      }
      await prisma.guide.delete({
        where: { id: guideId },
      });
      logger.info("Guide deleted", { guideId, userId });
      return { success: true };
    } catch (error) {
      logger.error("Error deleting guide:", error);
      throw error;
    }
  }

  /**
   * Get guides list with filtering and pagination
   */
  async getGuides(options: {
    userId?: string;
    organizationId?: string;
    status?: GuideStatus;
    visibility?: GuideVisibility;
    search?: string;
    page?: number;
    limit?: number;
  }) {
    const {
      userId,
      organizationId,
      status,
      visibility,
      search,
      page = 1,
      limit = 20,
    } = options;
    try {
      const where: Prisma.GuideWhereInput = {};
      if (userId) where.userId = userId;
      if (organizationId) where.organizationId = organizationId;
      if (status) where.status = status;
      if (visibility) where.visibility = visibility;
      if (search) {
        where.OR = [
          { title: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }
      const [guides, total] = await Promise.all([
        prisma.guide.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
              },
            },
            steps: {
              orderBy: { stepNumber: "asc" },
              include: { media: true },
            },
            comments: true,
          },
          orderBy: { createdAt: "desc" },
          skip: (page - 1) * limit,
          take: limit,
        }),
        prisma.guide.count({ where }),
      ]);
      return {
        guides,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      logger.error("Error getting guides:", error);
      throw error;
    }
  }
}

export const guideService = new GuideService();
export default guideService;
