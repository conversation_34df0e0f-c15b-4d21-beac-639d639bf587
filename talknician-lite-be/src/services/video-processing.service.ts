import { triggerPusherEvent } from "./pusher.service";
import { logger } from "@/utils/logger";

export interface VideoProcessingStatus {
  guideId: string;
  userId: string;
  organizationId: string;
  status: 'PENDING' | 'DOWNLOADING' | 'CONVERTING_TO_AUDIO' | 'TRANSCRIBING' | 'UPLOADING' | 'GENERATING_SUMMARY' | 'GENERATING_STEPS' | 'COMPLETED' | 'FAILED';
  progress?: number; // 0-100
  message?: string;
  error?: string;
  externalJobId?: string;
  srtUrl?: string;
  estimatedTimeRemaining?: number; // seconds
}

export interface VideoProcessingResult {
  guideId: string;
  status: 'COMPLETED' | 'FAILED';
  transcription?: string;
  summary?: string;
  steps?: Array<{
    title: string;
    description: string;
    startTime?: number;
    endTime?: number;
  }>;
  error?: string;
}

class VideoProcessingEventService {
  /**
   * Send video processing status update to user-specific channel
   */
  sendStatusUpdate(statusUpdate: VideoProcessingStatus): void {
    const { userId, organizationId } = statusUpdate;
    const channel = `private-org-${organizationId}-user-${userId}`;
    
    logger.info("Sending video processing status update", {
      channel,
      guideId: statusUpdate.guideId,
      status: statusUpdate.status,
      progress: statusUpdate.progress,
    });

    triggerPusherEvent(channel, "video-processing-status", statusUpdate);
  }

  /**
   * Send processing completion notification
   */
  sendProcessingComplete(result: VideoProcessingResult): void {
    // This would be called after the guide is fully processed
    logger.info("Video processing completed", {
      guideId: result.guideId,
      status: result.status,
    });
  }

  /**
   * Send error notification
   */
  sendProcessingError(
    guideId: string,
    userId: string,
    organizationId: string,
    error: string,
    externalJobId?: string
  ): void {
    const statusUpdate: VideoProcessingStatus = {
      guideId,
      userId,
      organizationId,
      status: 'FAILED',
      error,
      externalJobId,
      progress: 0,
    };

    this.sendStatusUpdate(statusUpdate);
  }

  /**
   * Send progress update with estimated time
   */
  sendProgressUpdate(
    guideId: string,
    userId: string,
    organizationId: string,
    status: VideoProcessingStatus['status'],
    progress: number,
    message?: string,
    estimatedTimeRemaining?: number
  ): void {
    const statusUpdate: VideoProcessingStatus = {
      guideId,
      userId,
      organizationId,
      status,
      progress,
      message,
      estimatedTimeRemaining,
    };

    this.sendStatusUpdate(statusUpdate);
  }
}

export const videoProcessingEventService = new VideoProcessingEventService();
export default videoProcessingEventService;
