import { Queue, Worker, Job, QueueEvents } from 'bullmq';
import Redis from 'ioredis';
import { logger } from '@/utils/logger';
import axios from 'axios';
import { videoProcessingEventService } from './video-processing.service';

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
};

// Create Redis connection
const redis = new Redis(redisConfig);

// Queue names
export const QUEUE_NAMES = {
  VIDEO_PROCESSING: 'video-processing',
  GUIDE_GENERATION: 'guide-generation',
} as const;

// Job data interfaces
export interface VideoProcessingJobData {
  guideId: string;
  userId: string;
  organizationId: string;
  videoUrl: string;
  externalJobId?: string;
}

export interface GuideGenerationJobData {
  guideId: string;
  userId: string;
  organizationId: string;
  srtUrl: string;
  transcription: string;
}

class QueueService {
  private videoProcessingQueue: Queue<VideoProcessingJobData>;
  private guideGenerationQueue: Queue<GuideGenerationJobData>;
  private videoProcessingWorker: Worker<VideoProcessingJobData>;
  private guideGenerationWorker: Worker<GuideGenerationJobData>;
  private queueEvents: QueueEvents;

  constructor() {
    // Initialize queues
    this.videoProcessingQueue = new Queue(QUEUE_NAMES.VIDEO_PROCESSING, {
      connection: redis,
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    this.guideGenerationQueue = new Queue(QUEUE_NAMES.GUIDE_GENERATION, {
      connection: redis,
      defaultJobOptions: {
        removeOnComplete: 10,
        removeOnFail: 5,
        attempts: 2,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    });

    // Initialize workers
    this.videoProcessingWorker = new Worker(
      QUEUE_NAMES.VIDEO_PROCESSING,
      this.processVideoJob.bind(this),
      {
        connection: redis,
        concurrency: 3, // Process up to 3 videos simultaneously
      }
    );

    this.guideGenerationWorker = new Worker(
      QUEUE_NAMES.GUIDE_GENERATION,
      this.processGuideGeneration.bind(this),
      {
        connection: redis,
        concurrency: 5, // Process up to 5 guide generations simultaneously
      }
    );

    // Initialize queue events for monitoring
    this.queueEvents = new QueueEvents(QUEUE_NAMES.VIDEO_PROCESSING, {
      connection: redis,
    });

    this.setupEventListeners();
    logger.info('Queue service initialized successfully');
  }

  private setupEventListeners() {
    // Video processing job events
    this.videoProcessingWorker.on('completed', (job: Job<VideoProcessingJobData>) => {
      logger.info(`Video processing job ${job.id} completed`, {
        guideId: job.data.guideId,
        userId: job.data.userId,
      });
    });

    this.videoProcessingWorker.on('failed', (job: Job<VideoProcessingJobData> | undefined, err: Error) => {
      logger.error(`Video processing job ${job?.id} failed`, {
        error: err.message,
        guideId: job?.data.guideId,
        userId: job?.data.userId,
      });

      if (job?.data) {
        videoProcessingEventService.sendProcessingError(
          job.data.guideId,
          job.data.userId,
          job.data.organizationId,
          `Job failed: ${err.message}`,
          job.data.externalJobId
        );
      }
    });

    // Guide generation job events
    this.guideGenerationWorker.on('completed', (job: Job<GuideGenerationJobData>) => {
      logger.info(`Guide generation job ${job.id} completed`, {
        guideId: job.data.guideId,
        userId: job.data.userId,
      });
    });

    this.guideGenerationWorker.on('failed', (job: Job<GuideGenerationJobData> | undefined, err: Error) => {
      logger.error(`Guide generation job ${job?.id} failed`, {
        error: err.message,
        guideId: job?.data.guideId,
        userId: job?.data.userId,
      });
    });
  }

  /**
   * Add video processing job to queue
   */
  async addVideoProcessingJob(data: VideoProcessingJobData): Promise<Job<VideoProcessingJobData>> {
    const job = await this.videoProcessingQueue.add('process-video', data, {
      jobId: `video-${data.guideId}`, // Unique job ID to prevent duplicates
      delay: 1000, // Small delay to ensure database is updated
    });

    logger.info('Video processing job added to queue', {
      jobId: job.id,
      guideId: data.guideId,
      userId: data.userId,
    });

    return job;
  }

  /**
   * Add guide generation job to queue
   */
  async addGuideGenerationJob(data: GuideGenerationJobData): Promise<Job<GuideGenerationJobData>> {
    const job = await this.guideGenerationQueue.add('generate-guide', data, {
      jobId: `guide-${data.guideId}`, // Unique job ID to prevent duplicates
    });

    logger.info('Guide generation job added to queue', {
      jobId: job.id,
      guideId: data.guideId,
      userId: data.userId,
    });

    return job;
  }

  /**
   * Process video job - calls external Python API
   */
  private async processVideoJob(job: Job<VideoProcessingJobData>): Promise<void> {
    const { guideId, userId, organizationId, videoUrl } = job.data;
    
    try {
      logger.info(`Processing video job for guide ${guideId}`);

      // Send initial status update
      videoProcessingEventService.sendProgressUpdate(
        guideId,
        userId,
        organizationId,
        'PENDING',
        5,
        'Submitting video for processing...'
      );

      // Call external Python API
      const VIDEO_TO_SRT_API_URL = process.env.VIDEO_TO_SRT_API_URL || 'http://localhost:8000';
      const response = await axios.post(`${VIDEO_TO_SRT_API_URL}/jobs/`, {
        video_url: videoUrl,
        user_id: userId,
        organization_id: organizationId,
        guide_id: guideId,
      });

      const externalJobId = response.data.job_id;
      
      // Update job data with external job ID
      job.updateData({
        ...job.data,
        externalJobId: externalJobId.toString(),
      });

      logger.info(`External job ${externalJobId} created for guide ${guideId}`);

      // The Python backend will now handle the processing and send real-time updates via Pusher
      // This job is considered complete once the external job is submitted
      
    } catch (error) {
      logger.error(`Video processing job failed for guide ${guideId}:`, error);
      
      videoProcessingEventService.sendProcessingError(
        guideId,
        userId,
        organizationId,
        `Failed to submit video for processing: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
      
      throw error;
    }
  }

  /**
   * Process guide generation job - handles AI generation after transcription
   */
  private async processGuideGeneration(job: Job<GuideGenerationJobData>): Promise<void> {
    const { guideId, userId, organizationId, srtUrl, transcription } = job.data;
    
    try {
      logger.info(`Processing guide generation for guide ${guideId}`);

      // This will be implemented in the next phase with enhanced AI generation
      // For now, we'll use the existing completeGuideProcessing method
      
      const { guideService } = await import('./guide.service');
      await guideService.completeGuideProcessing(guideId, 'COMPLETED', srtUrl);
      
    } catch (error) {
      logger.error(`Guide generation job failed for guide ${guideId}:`, error);
      throw error;
    }
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    const videoStats = await this.videoProcessingQueue.getJobCounts();
    const guideStats = await this.guideGenerationQueue.getJobCounts();

    return {
      videoProcessing: videoStats,
      guideGeneration: guideStats,
    };
  }

  /**
   * Clean up completed and failed jobs
   */
  async cleanupJobs() {
    await this.videoProcessingQueue.clean(24 * 60 * 60 * 1000, 10, 'completed'); // Keep completed jobs for 24 hours
    await this.videoProcessingQueue.clean(7 * 24 * 60 * 60 * 1000, 5, 'failed'); // Keep failed jobs for 7 days
    
    await this.guideGenerationQueue.clean(24 * 60 * 60 * 1000, 10, 'completed');
    await this.guideGenerationQueue.clean(7 * 24 * 60 * 60 * 1000, 5, 'failed');
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    logger.info('Shutting down queue service...');
    
    await this.videoProcessingWorker.close();
    await this.guideGenerationWorker.close();
    await this.videoProcessingQueue.close();
    await this.guideGenerationQueue.close();
    await this.queueEvents.close();
    await redis.quit();
    
    logger.info('Queue service shut down successfully');
  }
}

export const queueService = new QueueService();
export default queueService;
