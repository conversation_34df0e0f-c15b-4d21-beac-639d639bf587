import { openai } from "./openai.service";
import { logger } from "@/utils/logger";

export interface GuideStepData {
  title: string;
  description: string;
  startTime?: number;
  endTime?: number;
  stepSummary?: string;
  keyPoints?: string[];
  actionItems?: string[];
}

export interface StructuredGuideResponse {
  summary: string;
  overview: string;
  estimatedDuration: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  steps: GuideStepData[];
  keyTakeaways: string[];
  troubleshooting?: {
    issue: string;
    solution: string;
  }[];
}

export interface SRTSegment {
  index: number;
  startTime: string;
  endTime: string;
  text: string;
  startSeconds: number;
  endSeconds: number;
}

class AIGuideGeneratorService {
  /**
   * Parse SRT content into structured segments
   */
  private parseSRTContent(srtContent: string): SRTSegment[] {
    const segments: SRTSegment[] = [];
    const blocks = srtContent.trim().split('\n\n');

    for (const block of blocks) {
      const lines = block.trim().split('\n');
      if (lines.length >= 3) {
        const index = parseInt(lines[0]);
        const timeRange = lines[1];
        const text = lines.slice(2).join(' ');

        // Parse time range (e.g., "00:00:01,000 --> 00:00:05,500")
        const timeMatch = timeRange.match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);
        if (timeMatch) {
          const startTime = timeMatch[1];
          const endTime = timeMatch[2];
          const startSeconds = this.timeToSeconds(startTime);
          const endSeconds = this.timeToSeconds(endTime);

          segments.push({
            index,
            startTime,
            endTime,
            text,
            startSeconds,
            endSeconds,
          });
        }
      }
    }

    return segments;
  }

  /**
   * Convert SRT timestamp to seconds
   */
  private timeToSeconds(timeStr: string): number {
    const [time, ms] = timeStr.split(',');
    const [hours, minutes, seconds] = time.split(':').map(Number);
    return hours * 3600 + minutes * 60 + seconds + Number(ms) / 1000;
  }

  /**
   * Convert seconds to SRT timestamp format
   */
  private secondsToTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const ms = Math.floor((seconds % 1) * 1000);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
  }

  /**
   * Generate enhanced guide summary using structured output
   */
  async generateEnhancedSummary(transcription: string): Promise<string> {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are an expert technical writer who creates comprehensive summaries of instructional videos. 
            Create a detailed, engaging summary that captures the main purpose, key concepts, and value proposition of the content.
            Focus on what viewers will learn and accomplish by following this guide.`
          },
          {
            role: "user",
            content: `Create a comprehensive summary for this instructional video transcript:\n\n${transcription}`
          }
        ],
        max_tokens: 300,
        temperature: 0.7,
      });

      return response.choices[0]?.message?.content || "Summary generation failed";
    } catch (error) {
      logger.error("Error generating enhanced summary:", error);
      return "Summary generation failed";
    }
  }

  /**
   * Generate structured guide with timestamps using OpenAI function calling
   */
  async generateStructuredGuide(srtContent: string): Promise<StructuredGuideResponse> {
    try {
      const segments = this.parseSRTContent(srtContent);
      const fullTranscription = segments.map(s => s.text).join(' ');

      const response = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are an expert instructional designer who creates comprehensive, structured guides from video transcriptions.
            
            Your task is to analyze the transcription and create a detailed guide with:
            1. A clear overview and summary
            2. Step-by-step instructions with timestamps
            3. Key points and action items for each step
            4. Difficulty assessment and prerequisites
            5. Key takeaways and troubleshooting tips
            
            Use the provided timestamps to create accurate time-based segments for each step.
            Make the guide practical, actionable, and easy to follow.`
          },
          {
            role: "user",
            content: `Create a comprehensive structured guide from this video transcription with timestamps:

${srtContent}

Please provide a detailed response with all the required fields.`
          }
        ],
        functions: [
          {
            name: "create_structured_guide",
            description: "Create a comprehensive structured guide from video transcription",
            parameters: {
              type: "object",
              properties: {
                summary: {
                  type: "string",
                  description: "A comprehensive 2-3 sentence summary of what this guide teaches"
                },
                overview: {
                  type: "string",
                  description: "A detailed overview explaining the purpose and scope of this guide"
                },
                estimatedDuration: {
                  type: "number",
                  description: "Estimated time in minutes to complete this guide"
                },
                difficulty: {
                  type: "string",
                  enum: ["beginner", "intermediate", "advanced"],
                  description: "Difficulty level of this guide"
                },
                prerequisites: {
                  type: "array",
                  items: { type: "string" },
                  description: "List of prerequisites or requirements before starting"
                },
                steps: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      title: { type: "string", description: "Clear, action-oriented step title" },
                      description: { type: "string", description: "Detailed description of what to do in this step" },
                      startTime: { type: "number", description: "Start time in seconds (optional)" },
                      endTime: { type: "number", description: "End time in seconds (optional)" },
                      keyPoints: {
                        type: "array",
                        items: { type: "string" },
                        description: "Key points or important notes for this step"
                      },
                      actionItems: {
                        type: "array",
                        items: { type: "string" },
                        description: "Specific actions the user should take"
                      }
                    },
                    required: ["title", "description"]
                  },
                  description: "Step-by-step instructions"
                },
                keyTakeaways: {
                  type: "array",
                  items: { type: "string" },
                  description: "Main lessons or insights from this guide"
                },
                troubleshooting: {
                  type: "array",
                  items: {
                    type: "object",
                    properties: {
                      issue: { type: "string", description: "Common issue or problem" },
                      solution: { type: "string", description: "How to solve the issue" }
                    },
                    required: ["issue", "solution"]
                  },
                  description: "Common issues and their solutions"
                }
              },
              required: ["summary", "overview", "estimatedDuration", "difficulty", "prerequisites", "steps", "keyTakeaways"]
            }
          }
        ],
        function_call: { name: "create_structured_guide" },
        max_tokens: 2000,
        temperature: 0.7,
      });

      const functionCall = response.choices[0]?.message?.function_call;
      if (!functionCall || !functionCall.arguments) {
        throw new Error("No function call response received");
      }

      const structuredGuide: StructuredGuideResponse = JSON.parse(functionCall.arguments);
      
      // Enhance steps with timestamp information from SRT segments
      structuredGuide.steps = this.enhanceStepsWithTimestamps(structuredGuide.steps, segments);

      logger.info("Structured guide generated successfully", {
        stepsCount: structuredGuide.steps.length,
        difficulty: structuredGuide.difficulty,
        estimatedDuration: structuredGuide.estimatedDuration,
      });

      return structuredGuide;

    } catch (error) {
      logger.error("Error generating structured guide:", error);
      
      // Fallback to basic structure
      return this.createFallbackGuide(srtContent);
    }
  }

  /**
   * Enhance steps with timestamp information from SRT segments
   */
  private enhanceStepsWithTimestamps(steps: GuideStepData[], segments: SRTSegment[]): GuideStepData[] {
    if (segments.length === 0) return steps;

    const totalDuration = segments[segments.length - 1]?.endSeconds || 0;
    const stepDuration = totalDuration / steps.length;

    return steps.map((step, index) => {
      const startTime = index * stepDuration;
      const endTime = Math.min((index + 1) * stepDuration, totalDuration);

      return {
        ...step,
        startTime: Math.round(startTime),
        endTime: Math.round(endTime),
      };
    });
  }

  /**
   * Create a fallback guide structure when AI generation fails
   */
  private createFallbackGuide(srtContent: string): StructuredGuideResponse {
    const segments = this.parseSRTContent(srtContent);
    const transcription = segments.map(s => s.text).join(' ');

    return {
      summary: "This guide provides step-by-step instructions based on the video content.",
      overview: "A comprehensive guide created from video transcription. Please review and edit as needed.",
      estimatedDuration: Math.max(5, Math.round(segments.length * 0.5)), // Rough estimate
      difficulty: "intermediate" as const,
      prerequisites: ["Basic understanding of the topic"],
      steps: [
        {
          title: "Follow Video Instructions",
          description: transcription.length > 500 ? 
            transcription.substring(0, 500) + "..." : 
            transcription || "Please add step descriptions manually.",
          startTime: segments[0]?.startSeconds || 0,
          endTime: segments[segments.length - 1]?.endSeconds || 0,
          keyPoints: ["Review the video content carefully"],
          actionItems: ["Follow along with the video"],
        }
      ],
      keyTakeaways: ["Complete the steps as demonstrated in the video"],
      troubleshooting: [
        {
          issue: "Steps are unclear",
          solution: "Review the original video and edit the guide manually"
        }
      ],
    };
  }

  /**
   * Generate guide steps with enhanced structure (legacy method for compatibility)
   */
  async generateGuideSteps(transcription: string): Promise<GuideStepData[]> {
    try {
      const structuredGuide = await this.generateStructuredGuide(transcription);
      return structuredGuide.steps;
    } catch (error) {
      logger.error("Error in generateGuideSteps:", error);
      return [
        {
          title: "Manual Step Required",
          description: "Please add steps manually as auto-generation failed.",
          keyPoints: ["Review the transcription"],
          actionItems: ["Create steps based on video content"],
        }
      ];
    }
  }
}

export const aiGuideGeneratorService = new AIGuideGeneratorService();
export default aiGuideGeneratorService;
