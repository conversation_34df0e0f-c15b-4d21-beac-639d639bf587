import express from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import rateLimit from "express-rate-limit";
import { config } from "@/config";
import { logger } from "@/utils/logger";
import authRoutes from "@/routes/auth.routes";
import organizationRoutes from "@/routes/organization.routes";
import chatRoutes from "@/routes/chat.routes";
import documentRoutes from "@/routes/documents.routes";
import personaRoutes from "@/routes/persona.routes";
import session from "express-session";
import onedriveRoutes from "@/routes/onedrive.routes";
import webscrapingRoutes from "@/routes/webscraping.routes";
import userRoutes from "@/routes/user.routes";
import packageJson from "../package.json";
import pusherRoutes from "./routes/pusher.routes";
import messageDocumentRoutes from "./routes/message-document.routes";
import guidesRoutes from "@/routes/guides.routes";

// Create Express application
const app: express.Application = express();

// Trust proxy for rate limiting
app.set("trust proxy", 1);

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: false, // Disable for API
    crossOriginEmbedderPolicy: false,
  })
);

// CORS configuration
app.use(cors(config.cors));

// Compression middleware
app.use(compression());

// Add after compression and before routes
app.use(
  session({
    secret:
      process.env.ONEDRIVE_SESSION_SECRET ||
      "demo_session_secret_change_in_production",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: false, // set to true if using HTTPS
      httpOnly: true,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 1 week
    },
  })
);

// Body parsing middleware - conditionally apply JSON parsing
app.use((req, res, next) => {
  // Skip JSON parsing for file upload routes
  if (
    req.url.includes("/api/documents/upload") ||
    req.url.includes("/api/message-documents/upload")
  ) {
    logger.info("Skipping JSON parsing for upload route", { url: req.url });
    return next();
  }

  // Apply JSON parsing for other routes
  express.json({ limit: "10mb" })(req, res, next);
});

app.use((req, res, next) => {
  // Skip URL encoding for file upload routes
  if (
    req.url.includes("/api/documents/upload") ||
    req.url.includes("/api/message-documents/upload")
  ) {
    logger.info("Skipping URL encoding for upload route", { url: req.url });
    return next();
  }

  // Apply URL encoding for other routes
  express.urlencoded({ extended: true, limit: "10mb" })(req, res, next);
});

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    error: "Too Many Requests",
    message: "Rate limit exceeded. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Extract only the IP address (remove port if present)
    const ip = req.ip || "unknown";
    // IPv6 format ::ffff:127.0.0.1 or ::1
    if (ip.includes(":")) {
      // Remove port if present (e.g., '**************:36996')
      const lastPart = ip.split(":").slice(-1)[0];
      return lastPart && lastPart.match(/^\d+\.\d+\.\d+\.\d+$/) ? lastPart : ip;
    }
    return ip;
  },
});

app.use("/api/", limiter);

// Log every incoming request (simplified)
app.use((req, res, next) => {
  logger.info("Incoming request", {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
  });
  next();
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    environment: config.env,
    version: packageJson.version,
  });
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/organizations", organizationRoutes);
app.use("/api/chat", chatRoutes);
app.use("/api/documents", documentRoutes);
app.use("/api/personas", personaRoutes);
app.use("/api/integrations/onedrive", onedriveRoutes);
app.use("/api/webscraping", webscrapingRoutes);
app.use("/api/pusher", pusherRoutes);
app.use("/api/users", userRoutes);
app.use("/api/message-documents", messageDocumentRoutes);
app.use("/api/guides", guidesRoutes);

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    error: "Not Found",
    message: "The requested resource was not found on this server.",
    path: req.originalUrl,
  });
});

// Global error handler
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    logger.error("Unhandled error", {
      error: err.message,
      stack: err.stack,
      url: req.url,
      method: req.method,
    });

    // Don't leak error details in production
    const isDevelopment = config.isDevelopment;

    res.status(err.status || 500).json({
      error: "Internal Server Error",
      message: isDevelopment ? err.message : "Something went wrong",
      ...(isDevelopment && { stack: err.stack }),
    });
  }
);

// Start server
const startServer = async () => {
  try {
    app.listen(config.port, () => {
      logger.info("Server started successfully", {
        port: config.port,
        environment: config.env,
        nodeVersion: process.version,
      });

      console.log(`
🚀 Talknician Lite API Server
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🌐 Server running on: http://localhost:${config.port}
📊 Health check: http://localhost:${config.port}/health
 Environment: ${config.env}
 Version: ${packageJson.version}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
      `);
    });
  } catch (error) {
    logger.error("Failed to start server", { error });
    process.exit(1);
  }
};

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception", {
    error: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

// Handle unhandled promise rejections
process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection", { reason, promise });
  process.exit(1);
});

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down gracefully");
  process.exit(0);
});

process.on("SIGINT", () => {
  logger.info("SIGINT received, shutting down gracefully");
  process.exit(0);
});

// Start the server
startServer();

export default app;
