import { z } from "zod";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Environment schema validation
const envSchema = z.object({
  NODE_ENV: z
    .enum(["development", "production", "test"])
    .default("development"),
  PORT: z.string().transform(Number).default("3002"),

  // Database
  DATABASE_URL: z.string().min(1, "Database URL is required"),

  // Redis
  REDIS_HOST: z.string().default("localhost"),
  REDIS_PORT: z.string().transform(Number).default("6379"),
  REDIS_PASSWORD: z.string().optional(),
  REDIS_MAXMEMORY_POLICY: z
    .enum([
      "noeviction",
      "allkeys-lru",
      "volatile-lru",
      "allkeys-random",
      "volatile-random",
      "volatile-ttl",
    ])
    .default("noeviction"),

  // Auth0
  AUTH0_DOMAIN: z.string().min(1, "Auth0 domain is required"),
  AUTH0_CLIENT_ID: z.string().min(1, "Auth0 client ID is required"),
  AUTH0_CLIENT_SECRET: z.string().min(1, "Auth0 client secret is required"),
  AUTH0_AUDIENCE: z.string().min(1, "Auth0 audience is required"),
  AUTH0_ALGORITHM: z.string().default("RS256"),

  // Management API
  AUTH0_MANAGEMENT_CLIENT_ID: z.string().optional(),
  AUTH0_MANAGEMENT_CLIENT_SECRET: z.string().optional(),

  // JWT
  JWT_SECRET: z.string().min(1, "JWT secret is required"),

  // Frontend
  FRONTEND_URL: z.string().url("Valid frontend URL is required"),

  // OpenAI
  OPENAI_API_KEY: z.string().min(1, "OpenAI API key is required"),

  // Azure Storage
  AZURE_STORAGE_ACCOUNT_NAME: z
    .string()
    .min(1, "Azure storage account name is required"),
  AZURE_STORAGE_ACCOUNT_KEY: z
    .string()
    .min(1, "Azure storage account key is required"),
  AZURE_STORAGE_CONTAINER_NAME: z
    .string()
    .min(1, "Azure storage container name is required"),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default("900000"),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default("********"),

  // Logging
  LOG_LEVEL: z.enum(["error", "warn", "info", "debug"]).default("info"),
});

// Validate environment variables
const env = envSchema.parse(process.env);

export const config = {
  env: env.NODE_ENV,
  port: env.PORT,
  isDevelopment: env.NODE_ENV === "development",
  isProduction: env.NODE_ENV === "production",

  database: {
    url: env.DATABASE_URL,
  },

  redis: {
    host: env.REDIS_HOST,
    port: env.REDIS_PORT,
    password: env.REDIS_PASSWORD,
    maxMemoryPolicy: env.REDIS_MAXMEMORY_POLICY,
  },

  auth0: {
    domain: env.AUTH0_DOMAIN,
    clientId: env.AUTH0_CLIENT_ID,
    clientSecret: env.AUTH0_CLIENT_SECRET,
    audience: env.AUTH0_AUDIENCE,
    algorithm: env.AUTH0_ALGORITHM as "RS256" | "HS256",
    issuer: `https://${env.AUTH0_DOMAIN}/`,
    jwksUri: `https://${env.AUTH0_DOMAIN}/.well-known/jwks.json`,
  },

  management: {
    clientId: env.AUTH0_MANAGEMENT_CLIENT_ID,
    clientSecret: env.AUTH0_MANAGEMENT_CLIENT_SECRET,
  },

  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: "24h",
    refreshExpiresIn: "7d",
  },

  frontend: {
    url: env.FRONTEND_URL,
  },

  openai: {
    apiKey: env.OPENAI_API_KEY,
    model: "gpt-4-turbo-preview",
    maxTokens: 4000,
  },

  azure: {
    storage: {
      accountName: env.AZURE_STORAGE_ACCOUNT_NAME,
      accountKey: env.AZURE_STORAGE_ACCOUNT_KEY,
      containerName: env.AZURE_STORAGE_CONTAINER_NAME,
    },
  },

  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },

  logging: {
    level: env.LOG_LEVEL,
  },

  cors: {
    origin: [env.FRONTEND_URL] as string[],
    credentials: true,
  },
} as const;

export default config;
