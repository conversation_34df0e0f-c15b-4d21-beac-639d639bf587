// NOTE: This route expects authentication middleware to set req.user = { id, organizations: [{ id, ... }] }
import express, { Request, Response } from "express";
import multer from "multer";
import path from "path";
import { authenticate } from "../middleware/auth.middleware";
import guideService from "../services/guide.service";
import { $Enums } from "@prisma/client";

const router: express.Router = express.Router();

// Configure multer for video uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB limit for videos
  },
  fileFilter: (req, file, cb) => {
    // Allow video file types
    const allowedTypes = [
      ".mp4",
      ".avi",
      ".mov",
      ".wmv",
      ".flv",
      ".webm",
      ".mkv",
    ];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error("Only video files are allowed"));
    }
  },
});

// Test route without authentication for debugging
router.post(
  "/test-upload",
  upload.single("video"),
  async (req: Request, res: Response) => {
    try {
      console.log("=== TEST UPLOAD DEBUG ===");
      console.log("Request body:", req.body);
      console.log("Request file:", req.file);
      console.log("Request headers:", req.headers);
      console.log("========================");

      const { title, description, visibility } = req.body;

      res.json({
        success: true,
        body: req.body,
        file: req.file
          ? {
              fieldname: req.file.fieldname,
              originalname: req.file.originalname,
              mimetype: req.file.mimetype,
              size: req.file.size,
            }
          : null,
        received: { title, description, visibility },
      });
    } catch (error: any) {
      console.error("Test upload error:", error);
      res.status(500).json({ error: error.message || "Unknown error" });
    }
  }
);

router.use(authenticate);

function getUserId(req: Request): string {
  if (!req.user || !req.user.id) throw new Error("User not authenticated");
  return req.user.id;
}
function getOrganizationId(req: Request): string {
  // Try to get organization ID from various sources
  const organizationId =
    req.params.organizationId ||
    req.body.organizationId ||
    req.query.organizationId;

  if (!organizationId) {
    throw new Error("Organization ID is required");
  }

  // Verify user has access to this organization
  if (!req.user || !req.user.organizations) {
    throw new Error("User not authenticated");
  }

  const userOrg = req.user.organizations.find(
    (org) => org.id === organizationId
  );

  if (!userOrg) {
    throw new Error("Access denied to this organization");
  }

  return organizationId as string;
}

// POST /guides/upload - Upload video and create guide
router.post(
  "/upload",
  upload.single("video"),
  async (req: Request, res: Response) => {
    try {
      // Debug logging
      console.log("Request body:", req.body);
      console.log("Request file:", req.file);
      console.log("Request headers:", req.headers);

      const { title, description, visibility } = req.body;
      const userId = getUserId(req);
      const organizationId = getOrganizationId(req);
      const videoFile = req.file;
      if (!videoFile)
        return res.status(400).json({ error: "No video file uploaded" });
      const result = await guideService.createGuideFromVideo({
        title,
        description,
        videoFile,
        userId,
        organizationId,
        visibility,
      });
      res.json(result);
    } catch (error: any) {
      res.status(500).json({ error: error.message || "Unknown error" });
    }
  }
);

// POST /guides/:id/complete - Client notifies backend of job completion
router.post("/:id/complete", async (req: Request, res: Response) => {
  try {
    const guideId = req.params.id;
    const { status, srtUrl } = req.body;
    await guideService.completeGuideProcessing(guideId, status, srtUrl);
    res.json({ success: true });
  } catch (error: any) {
    res.status(500).json({ error: error.message || "Unknown error" });
  }
});

// GET /guides/:id - Get guide by ID
router.get("/:id", async (req: Request, res: Response) => {
  try {
    const guideId = req.params.id;
    const userId = getUserId(req);
    const guide = await guideService.getGuideById(guideId, userId);
    res.json(guide);
  } catch (error: any) {
    res.status(404).json({ error: error.message || "Unknown error" });
  }
});

// PATCH /guides/:id - Update guide
router.patch("/:id", async (req: Request, res: Response) => {
  try {
    const guideId = req.params.id;
    const userId = getUserId(req);
    const updateData = req.body;
    const updatedGuide = await guideService.updateGuide(
      guideId,
      userId,
      updateData
    );
    res.json(updatedGuide);
  } catch (error: any) {
    res.status(400).json({ error: error.message || "Unknown error" });
  }
});

// DELETE /guides/:id - Delete guide
router.delete("/:id", async (req: Request, res: Response) => {
  try {
    const guideId = req.params.id;
    const userId = getUserId(req);
    await guideService.deleteGuide(guideId, userId);
    res.json({ success: true });
  } catch (error: any) {
    res.status(400).json({ error: error.message || "Unknown error" });
  }
});

// GET /guides - List guides
router.get("/", async (req: Request, res: Response) => {
  try {
    const { status, visibility, search, page, limit } = req.query;
    const userId = getUserId(req);
    const organizationId = getOrganizationId(req);
    const guides = await guideService.getGuides({
      userId,
      organizationId,
      status: status as $Enums.GuideStatus | undefined,
      visibility: visibility as $Enums.GuideVisibility | undefined,
      search: search ? String(search) : undefined,
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
    });
    res.json(guides);
  } catch (error: any) {
    res.status(500).json({ error: error.message || "Unknown error" });
  }
});

export default router;
