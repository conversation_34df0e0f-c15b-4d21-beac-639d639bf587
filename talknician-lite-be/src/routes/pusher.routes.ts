import express from "express";
import { authenticate } from "../middleware/auth.middleware";
import { pusher } from "../services/pusher.service";
import logger from "@/utils/logger";

const router: express.Router = express.Router();

router.post("/auth", authenticate, (req, res) => {
  if (!pusher) {
    return res.status(500).send("Pusher is not configured");
  }

  const socketId = req.body.socket_id;
  const channel = req.body.channel_name;
  const { organizationId } = req.body;
  const userId = req.user?.id;

  if (!userId || !organizationId) {
    return res.status(403).send("Forbidden");
  }

  // Support multiple channel patterns:
  // 1. Organization-wide: private-org-ORGANIZATION_ID
  // 2. User-specific: private-org-ORGANIZATION_ID-user-USER_ID
  const orgChannel = `private-org-${organizationId}`;
  const userChannel = `private-org-${organizationId}-user-${userId}`;

  if (channel !== orgChannel && channel !== userChannel) {
    logger.warn("Pusher auth failed: channel mismatch", {
      userId,
      organizationId,
      requestedChannel: channel,
      expectedChannels: [orgChannel, userChannel],
    });
    return res.status(403).send("Forbidden");
  }

  // Check if user is actually a member of the organization
  const userOrg = req.user?.organizations.find(
    (org) => org.id === organizationId
  );
  if (!userOrg) {
    logger.warn("Pusher auth failed: user not member of organization", {
      userId,
      organizationId,
    });
    return res.status(403).send("Forbidden");
  }

  const presenceData = {
    user_id: userId,
  };

  try {
    const auth = pusher.authorizeChannel(socketId, channel, presenceData);
    res.send(auth);
  } catch (error) {
    logger.error("Pusher authentication failed", { error });
    res.status(500).send("Pusher authentication error");
  }
});

export default router;
