#!/usr/bin/env python3
"""
Test script for the enhanced audio processor
"""
import os
import logging
from dotenv import load_dotenv
from app.audio_processor import EnhancedAudioProcessor

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def test_audio_processor():
    """Test the audio processor with a sample video"""
    try:
        # Initialize processor
        processor = EnhancedAudioProcessor(
            chunk_duration=30,  # 30 second chunks
            overlap_duration=2,  # 2 second overlap
            max_workers=3,
        )

        # Test with a sample video URL (you can replace this with your test video)
        video_url = "https://talknicianliteproduction.blob.core.windows.net/video/video-to-test-transcribe.mp4"

        logger.info(f"Testing with video: {video_url}")

        # Download video (simulate the download step)
        from app.utils import download_video

        video_path = download_video(video_url)
        logger.info(f"Downloaded video to: {video_path}")

        # Extract audio
        audio_path = processor.extract_audio_from_video(video_path)
        logger.info(f"Extracted audio to: {audio_path}")

        # Get audio duration
        duration = processor.get_audio_duration(audio_path)
        logger.info(f"Audio duration: {duration:.2f} seconds")

        # Use the full processing pipeline (will choose continuous vs chunked automatically)
        def progress_callback(progress, message):
            logger.info(f"Progress: {progress:.1f}% - {message}")

        srt_content = processor.process_audio_to_srt(audio_path, progress_callback)
        logger.info(f"Generated SRT content ({len(srt_content)} characters):")
        print("=" * 50)
        print(srt_content)
        print("=" * 50)

        # Cleanup is handled automatically by the processor

        # Clean up downloaded files
        if os.path.exists(video_path):
            os.remove(video_path)
        if os.path.exists(audio_path):
            os.remove(audio_path)

        logger.info("Test completed successfully!")

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        raise


if __name__ == "__main__":
    test_audio_processor()
