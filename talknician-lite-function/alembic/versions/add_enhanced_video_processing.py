"""Add enhanced video processing fields

Revision ID: add_enhanced_video_processing
Revises: 
Create Date: 2024-07-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_enhanced_video_processing'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Add new fields to video_jobs table for enhanced processing
    op.add_column('video_jobs', sa.Column('user_id', sa.String(), nullable=True))
    op.add_column('video_jobs', sa.Column('organization_id', sa.String(), nullable=True))
    op.add_column('video_jobs', sa.Column('guide_id', sa.String(), nullable=True))

def downgrade():
    # Remove the added columns
    op.drop_column('video_jobs', 'guide_id')
    op.drop_column('video_jobs', 'organization_id')
    op.drop_column('video_jobs', 'user_id')
