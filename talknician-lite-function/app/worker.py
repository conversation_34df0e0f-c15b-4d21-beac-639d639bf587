import logging
from sqlalchemy.orm import Session
from app.models import <PERSON><PERSON><PERSON>, JobStatus, SessionLocal
from app.utils import (
    download_video,
    upload_to_blob,
    cleanup_files,
)
from app.pusher_service import pusher_service
from app.audio_processor import EnhancedAudioProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def update_job_status(
    session: Session,
    job: VideoJob,
    status: JobStatus,
    error: str = None,
    progress: int = 0,
):
    """Update job status in database and send real-time notification"""
    job.status = status
    if error:
        job.error_message = error
    session.commit()
    logger.info(f"Job {job.id} status updated to {status.value} (error: {error})")

    # Send real-time notification if user info is available
    if job.user_id and job.organization_id and job.guide_id:
        if error:
            pusher_service.send_processing_error(
                user_id=job.user_id,
                organization_id=job.organization_id,
                guide_id=job.guide_id,
                error_message=error,
                external_job_id=str(job.id),
            )
        else:
            # Map job status to progress percentage
            progress_map = {
                JobStatus.PENDING: 5,
                JobStatus.DOWNLOADING: 20,
                JobStatus.CONVERTING_TO_AUDIO: 40,
                JobStatus.TRANSCRIBING: 60,
                JobStatus.UPLOADING: 80,
                JobStatus.COMPLETED: 100,
                JobStatus.FAILED: 0,
            }

            pusher_service.send_video_processing_update(
                user_id=job.user_id,
                organization_id=job.organization_id,
                guide_id=job.guide_id,
                status=status.value,
                progress=progress_map.get(status, progress),
                message=f"Processing step: {status.value.replace('_', ' ').title()}",
                external_job_id=str(job.id),
            )


def process_video_job(job_id: int):
    """Main function to process video-to-SRT job"""
    session = SessionLocal()
    video_path = None
    audio_path = None

    try:
        # Get job from database
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if not job:
            logger.error(f"Job {job_id} not found")
            return

        logger.info(f"Starting job {job_id}: {job.video_url}")

        # Step 1: Download video
        update_job_status(session, job, JobStatus.DOWNLOADING)
        logger.info(f"Downloading video for job {job_id} from {job.video_url}")
        video_path = download_video(job.video_url)
        logger.info(f"Video downloaded to: {video_path}")

        # Step 2: Initialize enhanced audio processor
        audio_processor = EnhancedAudioProcessor(
            chunk_duration=30,  # 30-second chunks
            overlap_duration=2,  # 2-second overlap
            max_workers=3,  # Process up to 3 chunks in parallel
        )

        # Step 3: Extract audio from video
        update_job_status(session, job, JobStatus.CONVERTING_TO_AUDIO)
        logger.info(f"Extracting audio from video for job {job_id}")
        audio_path = audio_processor.extract_audio_from_video(video_path)
        logger.info(f"Audio extracted to: {audio_path}")

        # Step 4: Process audio with chunking and parallel transcription
        update_job_status(session, job, JobStatus.TRANSCRIBING)
        logger.info(f"Starting enhanced transcription for job {job_id}")

        def progress_callback(progress, message):
            """Callback to send real-time progress updates"""
            if job.user_id and job.organization_id and job.guide_id:
                pusher_service.send_video_processing_update(
                    user_id=job.user_id,
                    organization_id=job.organization_id,
                    guide_id=job.guide_id,
                    status="TRANSCRIBING",
                    progress=int(
                        20 + (progress * 0.4)
                    ),  # Map to 20-60% of total progress
                    message=message,
                    external_job_id=str(job.id),
                )

        srt_text = audio_processor.process_audio_to_srt(audio_path, progress_callback)
        logger.info(
            f"Enhanced transcription completed for job {job_id}, SRT length: {len(srt_text)} characters"
        )

        # Step 5: Upload SRT to blob storage
        update_job_status(session, job, JobStatus.UPLOADING)
        logger.info(f"Uploading SRT for job {job_id} to blob storage")
        urls = upload_to_blob(job_id, srt_text)
        job.srt_url = urls["srt_url"]
        logger.info(f"SRT uploaded to: {urls['srt_url']}")

        # Step 6: Mark as completed and send final notification
        update_job_status(session, job, JobStatus.COMPLETED)

        # Send completion notification with SRT URL
        if job.user_id and job.organization_id and job.guide_id:
            pusher_service.send_processing_complete(
                user_id=job.user_id,
                organization_id=job.organization_id,
                guide_id=job.guide_id,
                srt_url=job.srt_url,
                external_job_id=str(job.id),
            )

        logger.info(f"Enhanced job {job_id} completed successfully")

    except Exception as e:
        import traceback

        logger.error(f"Job {job_id} failed: {str(e)}\n{traceback.format_exc()}")
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if job:
            update_job_status(session, job, JobStatus.FAILED, str(e))

    finally:
        # Clean up temporary files
        logger.info(
            f"Cleaning up files for job {job_id}: video_path={video_path}, audio_path={audio_path}"
        )
        cleanup_files(video_path, audio_path)
        session.close()
        logger.info(f"Cleanup completed for job {job_id}")


def get_job_status(job_id: int) -> dict:
    """Get job status and details"""
    session = SessionLocal()
    try:
        job = session.query(VideoJob).filter(VideoJob.id == job_id).first()
        if not job:
            return {"error": "Job not found"}

        return {
            "job_id": job.id,
            "video_url": job.video_url,
            "status": job.status.value,
            "created_at": job.created_at.isoformat(),
            "srt_url": job.srt_url,
            "error_message": job.error_message,
        }
    finally:
        session.close()
