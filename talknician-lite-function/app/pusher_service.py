import os
import logging
from typing import Optional, Dict, Any
import pusher

logger = logging.getLogger(__name__)

class PusherService:
    def __init__(self):
        self.pusher_client: Optional[pusher.Pusher] = None
        self._initialize_pusher()
    
    def _initialize_pusher(self):
        """Initialize Pusher client with environment variables"""
        app_id = os.getenv("PUSHER_APP_ID")
        key = os.getenv("PUSHER_KEY") 
        secret = os.getenv("PUSHER_SECRET")
        cluster = os.getenv("PUSHER_CLUSTER", "ap1")
        
        if not all([app_id, key, secret]):
            logger.warning("Pusher credentials not fully configured. Real-time updates disabled.")
            return
            
        try:
            self.pusher_client = pusher.Pusher(
                app_id=app_id,
                key=key,
                secret=secret,
                cluster=cluster,
                ssl=True
            )
            logger.info("Pusher client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Pusher client: {e}")
            self.pusher_client = None
    
    def send_video_processing_update(
        self,
        user_id: str,
        organization_id: str,
        guide_id: str,
        status: str,
        progress: int = 0,
        message: str = "",
        external_job_id: str = "",
        estimated_time_remaining: Optional[int] = None
    ) -> bool:
        """Send video processing status update to user-specific channel"""
        if not self.pusher_client:
            logger.warning("Pusher client not available. Skipping status update.")
            return False
            
        channel = f"private-org-{organization_id}-user-{user_id}"
        event_data = {
            "guideId": guide_id,
            "userId": user_id,
            "organizationId": organization_id,
            "status": status,
            "progress": progress,
            "message": message,
            "externalJobId": external_job_id,
            "timestamp": self._get_timestamp()
        }
        
        if estimated_time_remaining is not None:
            event_data["estimatedTimeRemaining"] = estimated_time_remaining
            
        try:
            self.pusher_client.trigger(channel, "video-processing-status", event_data)
            logger.info(f"Sent video processing update: {status} ({progress}%) to {channel}")
            return True
        except Exception as e:
            logger.error(f"Failed to send Pusher event: {e}")
            return False
    
    def send_processing_error(
        self,
        user_id: str,
        organization_id: str,
        guide_id: str,
        error_message: str,
        external_job_id: str = ""
    ) -> bool:
        """Send processing error notification"""
        return self.send_video_processing_update(
            user_id=user_id,
            organization_id=organization_id,
            guide_id=guide_id,
            status="FAILED",
            progress=0,
            message=f"Processing failed: {error_message}",
            external_job_id=external_job_id
        )
    
    def send_processing_complete(
        self,
        user_id: str,
        organization_id: str,
        guide_id: str,
        srt_url: str,
        external_job_id: str = ""
    ) -> bool:
        """Send processing completion notification"""
        return self.send_video_processing_update(
            user_id=user_id,
            organization_id=organization_id,
            guide_id=guide_id,
            status="COMPLETED",
            progress=100,
            message="Video processing completed successfully",
            external_job_id=external_job_id
        )
    
    def _get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.utcnow().isoformat() + "Z"

# Global instance
pusher_service = PusherService()
