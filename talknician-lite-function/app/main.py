from fastapi import FastAPI, BackgroundTasks, HTTPException, Depends
from pydantic import BaseModel, HttpUrl
from sqlalchemy.orm import Session
from app.models import <PERSON>Job, JobStatus, create_tables, get_db
from app.worker import process_video_job, get_job_status
import logging
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create tables on startup
create_tables()

app = FastAPI(
    title="Video-to-SRT API",
    description="Convert video files to SRT subtitle files using Azure Speech-to-Text",
    version="1.0.0",
)


# Pydantic models for API
class JobSubmissionRequest(BaseModel):
    video_url: HttpUrl
    user_id: str | None = None
    organization_id: str | None = None
    guide_id: str | None = None


class JobSubmissionResponse(BaseModel):
    job_id: int
    status: str
    message: str


class JobStatusResponse(BaseModel):
    job_id: int
    video_url: str
    status: str
    created_at: str
    srt_url: str | None = None
    error_message: str | None = None


@app.get("/")
def root():
    return {
        "message": "Video-to-SRT API",
        "version": "1.0.0",
        "endpoints": {
            "submit_job": "POST /jobs/",
            "check_status": "GET /jobs/{job_id}",
            "health": "GET /health",
        },
    }


@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "video-to-srt-api - 1.0.1"}


@app.post("/jobs/", response_model=JobSubmissionResponse)
def submit_job(
    request: JobSubmissionRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
):
    """Submit a new video-to-SRT conversion job"""
    try:
        # Validate required environment variables
        required_vars = [
            "AZURE_SPEECH_KEY",
            "AZURE_SPEECH_REGION",
            "AZURE_BLOB_CONNECTION",
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise HTTPException(
                status_code=500,
                detail=f"Missing required environment variables: {', '.join(missing_vars)}",
            )

        # Create new job
        job = VideoJob(
            video_url=str(request.video_url),
            status=JobStatus.PENDING,
            user_id=request.user_id,
            organization_id=request.organization_id,
            guide_id=request.guide_id,
        )

        db.add(job)
        db.commit()
        db.refresh(job)

        # Start background processing
        background_tasks.add_task(process_video_job, job.id)

        logger.info(f"Job {job.id} submitted for video: {request.video_url}")

        return JobSubmissionResponse(
            job_id=job.id, status=job.status.value, message="Job submitted successfully"
        )

    except Exception as e:
        logger.error(f"Error submitting job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/jobs/{job_id}", response_model=JobStatusResponse)
def check_job_status(job_id: int):
    """Check the status of a video-to-SRT conversion job"""
    try:
        job_data = get_job_status(job_id)

        if "error" in job_data:
            raise HTTPException(status_code=404, detail=job_data["error"])

        return JobStatusResponse(**job_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking job status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/jobs/{job_id}/download")
def download_srt(job_id: int):
    """Get download link for completed SRT file"""
    try:
        job_data = get_job_status(job_id)

        if "error" in job_data:
            raise HTTPException(status_code=404, detail=job_data["error"])

        if job_data["status"] != JobStatus.COMPLETED.value:
            raise HTTPException(
                status_code=400,
                detail=f"Job is not completed. Current status: {job_data['status']}",
            )

        if not job_data["srt_url"]:
            raise HTTPException(status_code=500, detail="SRT file URL not available")

        return {"download_url": job_data["srt_url"]}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting download link: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/jobs/")
def list_jobs(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)):
    """List all jobs with pagination"""
    try:
        jobs = db.query(VideoJob).offset(skip).limit(limit).all()

        return {
            "jobs": [
                {
                    "job_id": job.id,
                    "video_url": job.video_url,
                    "status": job.status.value,
                    "created_at": job.created_at.isoformat(),
                    "srt_url": job.srt_url,
                    "error_message": job.error_message,
                }
                for job in jobs
            ],
            "total": len(jobs),
        }

    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/debug/speech-test")
def test_speech_sdk():
    """Test Azure Speech SDK initialization"""
    try:
        import azure.cognitiveservices.speech as speechsdk

        speech_key = os.getenv("AZURE_SPEECH_KEY")
        speech_region = os.getenv("AZURE_SPEECH_REGION")

        if not speech_key or not speech_region:
            return {"error": "Azure Speech credentials not configured"}

        # Test SDK initialization
        speech_config = speechsdk.SpeechConfig(
            subscription=speech_key, region=speech_region
        )

        # Try to create a recognizer (this will test platform initialization)
        recognizer = speechsdk.SpeechRecognizer(speech_config=speech_config)

        return {
            "status": "success",
            "message": "Azure Speech SDK initialized successfully",
            "region": speech_region,
            "sdk_version": (
                speechsdk.__version__
                if hasattr(speechsdk, "__version__")
                else "unknown"
            ),
        }

    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "message": "Azure Speech SDK initialization failed",
        }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
