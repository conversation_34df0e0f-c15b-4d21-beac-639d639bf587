import os
import tempfile
import logging
from typing import List, Tuple, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import azure.cognitiveservices.speech as speechsdk
from moviepy.editor import AudioFileClip
from pydub import AudioSegment
import wave
import threading
import time

logger = logging.getLogger(__name__)


class AudioChunk:
    def __init__(
        self, start_time: float, end_time: float, audio_path: str, chunk_id: int
    ):
        self.start_time = start_time
        self.end_time = end_time
        self.audio_path = audio_path
        self.chunk_id = chunk_id
        self.transcription: Optional[str] = None
        self.srt_segment: Optional[str] = None


class EnhancedAudioProcessor:
    def __init__(
        self, chunk_duration: int = 30, overlap_duration: int = 2, max_workers: int = 3
    ):
        """
        Initialize the enhanced audio processor

        Args:
            chunk_duration: Duration of each audio chunk in seconds
            overlap_duration: Overlap between chunks to avoid word cutoffs
            max_workers: Maximum number of parallel transcription workers
        """
        self.chunk_duration = chunk_duration
        self.overlap_duration = overlap_duration
        self.max_workers = max_workers

        # Initialize Azure Speech SDK
        self.speech_config = self._initialize_speech_config()

    def _initialize_speech_config(self) -> speechsdk.SpeechConfig:
        """Initialize Azure Speech SDK configuration"""
        speech_key = os.getenv("AZURE_SPEECH_KEY")
        speech_region = os.getenv("AZURE_SPEECH_REGION")

        if not speech_key or not speech_region:
            raise ValueError("Azure Speech credentials not configured")

        speech_config = speechsdk.SpeechConfig(
            subscription=speech_key, region=speech_region
        )
        speech_config.speech_recognition_language = "en-US"
        speech_config.output_format = speechsdk.OutputFormat.Detailed

        # Optimize for better recognition of quiet speech
        speech_config.set_property(
            speechsdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs,
            "10000",
        )
        speech_config.set_property(
            speechsdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "3000"
        )
        speech_config.set_property(
            speechsdk.PropertyId.Speech_SegmentationSilenceTimeoutMs, "1000"
        )

        return speech_config

    def extract_audio_from_video(self, video_path: str) -> str:
        """Extract audio from video file optimized for speech recognition"""
        try:
            base, _ = os.path.splitext(video_path)
            audio_path = base + ".wav"

            # Use AudioFileClip for better compatibility
            audio_clip = AudioFileClip(video_path)

            # Optimize audio settings for Azure Speech SDK
            audio_clip.write_audiofile(
                audio_path,
                codec="pcm_s16le",
                ffmpeg_params=["-ac", "1", "-ar", "16000"],  # Mono, 16kHz
                verbose=False,
                logger=None,
            )
            audio_clip.close()

            logger.info(f"Audio extracted to: {audio_path}")
            return audio_path

        except Exception as e:
            logger.error(f"Audio extraction failed: {str(e)}")
            raise Exception(f"Audio extraction failed: {str(e)}")

    def get_audio_duration(self, audio_path: str) -> float:
        """Get audio duration in seconds"""
        try:
            audio = AudioSegment.from_wav(audio_path)
            return len(audio) / 1000.0  # Convert milliseconds to seconds
        except Exception as e:
            logger.error(f"Failed to get audio duration: {str(e)}")
            raise

    def create_audio_chunks(self, audio_path: str) -> List[AudioChunk]:
        """Split audio into overlapping chunks for parallel processing"""
        try:
            duration = self.get_audio_duration(audio_path)
            logger.info(f"Audio duration: {duration:.2f} seconds")

            # If audio is short, process as single chunk
            if duration <= self.chunk_duration:
                return [AudioChunk(0, duration, audio_path, 0)]

            chunks = []
            chunk_id = 0
            start_time = 0

            while start_time < duration:
                end_time = min(start_time + self.chunk_duration, duration)

                # Create chunk file
                chunk_path = self._create_chunk_file(
                    audio_path, start_time, end_time, chunk_id
                )
                chunk = AudioChunk(start_time, end_time, chunk_path, chunk_id)
                chunks.append(chunk)

                # Move to next chunk with overlap
                start_time += self.chunk_duration - self.overlap_duration
                chunk_id += 1

                # Break if we've covered the entire audio
                if end_time >= duration:
                    break

            logger.info(f"Created {len(chunks)} audio chunks")

            # Log chunk details for debugging
            for chunk in chunks:
                logger.debug(
                    f"Created chunk {chunk.chunk_id}: {chunk.start_time:.2f}s-{chunk.end_time:.2f}s"
                )

            return chunks

        except Exception as e:
            logger.error(f"Failed to create audio chunks: {str(e)}")
            raise

    def _create_chunk_file(
        self, audio_path: str, start_time: float, end_time: float, chunk_id: int
    ) -> str:
        """Create a chunk file from the main audio file"""
        try:
            audio = AudioSegment.from_wav(audio_path)

            # Extract chunk (times in milliseconds)
            start_ms = int(start_time * 1000)
            end_ms = int(end_time * 1000)
            chunk_audio = audio[start_ms:end_ms]

            # Create temporary file for chunk
            chunk_path = f"{audio_path}_chunk_{chunk_id}.wav"
            chunk_audio.export(chunk_path, format="wav")

            logger.debug(
                f"Created chunk {chunk_id}: {start_time:.2f}s - {end_time:.2f}s"
            )
            return chunk_path

        except Exception as e:
            logger.error(f"Failed to create chunk file: {str(e)}")
            raise

    def transcribe_chunk(self, chunk: AudioChunk) -> AudioChunk:
        """Transcribe a single audio chunk"""
        try:
            logger.debug(f"Transcribing chunk {chunk.chunk_id}")

            # Create audio config for the chunk
            audio_config = speechsdk.audio.AudioConfig(filename=chunk.audio_path)
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=self.speech_config, audio_config=audio_config
            )

            # Perform recognition
            result = speech_recognizer.recognize_once()

            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                chunk.transcription = result.text
                logger.debug(
                    f"Chunk {chunk.chunk_id} transcribed: {len(result.text)} characters"
                )
            elif result.reason == speechsdk.ResultReason.NoMatch:
                chunk.transcription = ""
                logger.debug(f"Chunk {chunk.chunk_id}: No speech detected")
            else:
                logger.warning(
                    f"Chunk {chunk.chunk_id} transcription failed: {result.reason}"
                )
                chunk.transcription = ""

            return chunk

        except Exception as e:
            logger.error(f"Transcription failed for chunk {chunk.chunk_id}: {str(e)}")
            chunk.transcription = ""
            return chunk

    def transcribe_chunks_parallel(
        self, chunks: List[AudioChunk], progress_callback=None
    ) -> List[AudioChunk]:
        """Transcribe multiple chunks in parallel"""
        try:
            logger.info(f"Starting parallel transcription of {len(chunks)} chunks")

            transcribed_chunks = {}  # Use dictionary to maintain order by chunk_id
            completed_count = 0

            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all transcription tasks
                future_to_chunk = {
                    executor.submit(self.transcribe_chunk, chunk): chunk
                    for chunk in chunks
                }

                # Process completed tasks
                for future in as_completed(future_to_chunk):
                    chunk = future_to_chunk[future]
                    try:
                        transcribed_chunk = future.result()
                        transcribed_chunks[transcribed_chunk.chunk_id] = (
                            transcribed_chunk
                        )
                        completed_count += 1

                        # Call progress callback if provided
                        if progress_callback:
                            progress = (completed_count / len(chunks)) * 100
                            progress_callback(
                                progress,
                                f"Transcribed chunk {completed_count}/{len(chunks)}",
                            )

                        logger.debug(
                            f"Completed transcription for chunk {transcribed_chunk.chunk_id}"
                        )

                    except Exception as e:
                        logger.error(f"Chunk transcription failed: {str(e)}")
                        # Keep the original chunk with empty transcription
                        transcribed_chunks[chunk.chunk_id] = chunk

            logger.info(
                f"Parallel transcription completed: {completed_count}/{len(chunks)} chunks"
            )

            # Log chunk details for debugging
            for chunk_id in sorted(transcribed_chunks.keys()):
                chunk = transcribed_chunks[chunk_id]
                logger.debug(
                    f"Chunk {chunk_id}: {chunk.start_time:.2f}s-{chunk.end_time:.2f}s, "
                    f"transcription: {len(chunk.transcription or '')} chars"
                )

            # Return chunks sorted by chunk_id
            result_chunks = [
                transcribed_chunks[chunk_id]
                for chunk_id in sorted(transcribed_chunks.keys())
            ]

            logger.info(f"Returning {len(result_chunks)} transcribed chunks")
            return result_chunks

        except Exception as e:
            logger.error(f"Parallel transcription failed: {str(e)}")
            raise

    def merge_transcriptions(self, chunks: List[AudioChunk]) -> str:
        """Merge chunk transcriptions into SRT format"""
        try:
            logger.info(f"Merging {len(chunks)} chunks into SRT format")
            srt_content = []
            subtitle_index = 1

            for chunk in sorted(chunks, key=lambda x: x.chunk_id):
                logger.debug(
                    f"Processing chunk {chunk.chunk_id}: {chunk.start_time:.2f}s-{chunk.end_time:.2f}s"
                )
                logger.debug(
                    f"Chunk {chunk.chunk_id} transcription: '{chunk.transcription}'"
                )

                if chunk.transcription and chunk.transcription.strip():
                    # Format timestamps for SRT
                    start_time = self._format_srt_timestamp(chunk.start_time)
                    end_time = self._format_srt_timestamp(chunk.end_time)

                    # Create SRT entry
                    srt_entry = f"{subtitle_index}\n{start_time} --> {end_time}\n{chunk.transcription.strip()}\n"
                    srt_content.append(srt_entry)
                    subtitle_index += 1
                    logger.debug(
                        f"Added SRT entry {subtitle_index-1} for chunk {chunk.chunk_id}"
                    )
                else:
                    logger.debug(f"Skipping chunk {chunk.chunk_id} - no transcription")

            logger.info(
                f"Created {len(srt_content)} SRT entries from {len(chunks)} chunks"
            )
            return "\n".join(srt_content)

        except Exception as e:
            logger.error(f"Failed to merge transcriptions: {str(e)}")
            raise

    def _format_srt_timestamp(self, seconds: float) -> str:
        """Format seconds to SRT timestamp format (HH:MM:SS,mmm)"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

    def cleanup_chunks(self, chunks: List[AudioChunk]):
        """Clean up temporary chunk files"""
        for chunk in chunks:
            try:
                if (
                    chunk.audio_path
                    and os.path.exists(chunk.audio_path)
                    and "_chunk_" in chunk.audio_path
                ):
                    os.remove(chunk.audio_path)
                    logger.debug(f"Cleaned up chunk file: {chunk.audio_path}")
            except Exception as e:
                logger.warning(
                    f"Failed to cleanup chunk file {chunk.audio_path}: {str(e)}"
                )

    def transcribe_continuous(self, audio_path: str, progress_callback=None) -> str:
        """Use continuous recognition for better transcription of long audio"""
        import threading

        try:
            if progress_callback:
                progress_callback(10, "Starting continuous transcription...")

            logger.info("Starting continuous transcription")

            # Create audio config
            audio_config = speechsdk.audio.AudioConfig(filename=audio_path)
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=self.speech_config, audio_config=audio_config
            )

            all_results = []
            done = threading.Event()

            def handle_result(evt):
                if evt.result.reason == speechsdk.ResultReason.RecognizedSpeech:
                    all_results.append(
                        {
                            "text": evt.result.text,
                            "offset": evt.result.offset,
                            "duration": evt.result.duration,
                        }
                    )
                    logger.info(f"Recognized: {evt.result.text}")
                    if progress_callback:
                        # Estimate progress based on number of results
                        estimated_progress = min(90, 20 + len(all_results) * 3)
                        progress_callback(
                            estimated_progress,
                            f"Transcribed {len(all_results)} segments...",
                        )

            def handle_session_stopped(evt):
                logger.info("Continuous recognition session stopped")
                done.set()

            def handle_canceled(evt):
                logger.error(f"Recognition canceled: {evt.result.cancellation_details}")
                done.set()

            # Connect event handlers
            speech_recognizer.recognized.connect(handle_result)
            speech_recognizer.session_stopped.connect(handle_session_stopped)
            speech_recognizer.canceled.connect(handle_canceled)

            # Start continuous recognition
            speech_recognizer.start_continuous_recognition()

            # Wait for completion with timeout
            if done.wait(timeout=1200):  # 20 minutes timeout
                speech_recognizer.stop_continuous_recognition()

                if progress_callback:
                    progress_callback(95, "Converting to SRT format...")

                # Convert results to SRT
                from app.utils import convert_to_srt

                srt_content = convert_to_srt(all_results)

                if progress_callback:
                    progress_callback(100, "Transcription completed")

                logger.info(
                    f"Continuous transcription completed with {len(all_results)} segments"
                )
                return srt_content
            else:
                speech_recognizer.stop_continuous_recognition()
                raise Exception("Continuous recognition timeout")

        except Exception as e:
            logger.error(f"Continuous transcription failed: {str(e)}")
            raise

    def process_audio_to_srt(self, audio_path: str, progress_callback=None) -> str:
        """Complete audio processing pipeline - try continuous first, fallback to chunked"""
        try:
            # Get audio duration to decide approach
            duration = self.get_audio_duration(audio_path)
            logger.info(f"Audio duration: {duration:.2f} seconds")

            # For longer audio (>60 seconds), use continuous recognition
            if duration > 60:
                logger.info("Using continuous recognition for long audio")
                return self.transcribe_continuous(audio_path, progress_callback)
            else:
                logger.info("Using chunked approach for short audio")
                return self._process_chunked(audio_path, progress_callback)

        except Exception as e:
            logger.error(f"Audio processing pipeline failed: {str(e)}")
            raise

    def _process_chunked(self, audio_path: str, progress_callback=None) -> str:
        """Fallback chunked processing for shorter audio"""
        chunks = []
        try:
            # Create audio chunks
            if progress_callback:
                progress_callback(10, "Creating audio chunks...")
            chunks = self.create_audio_chunks(audio_path)

            # Transcribe chunks in parallel
            if progress_callback:
                progress_callback(20, "Starting parallel transcription...")

            def transcription_progress(progress, message):
                # Map transcription progress to 20-80% of total progress
                total_progress = 20 + (progress * 0.6)
                if progress_callback:
                    progress_callback(total_progress, message)

            transcribed_chunks = self.transcribe_chunks_parallel(
                chunks, transcription_progress
            )

            # Merge transcriptions
            if progress_callback:
                progress_callback(90, "Merging transcriptions...")
            srt_content = self.merge_transcriptions(transcribed_chunks)

            if progress_callback:
                progress_callback(100, "Audio processing completed")

            logger.info("Chunked audio processing pipeline completed successfully")
            return srt_content

        except Exception as e:
            logger.error(f"Chunked audio processing pipeline failed: {str(e)}")
            raise
        finally:
            # Always cleanup chunk files
            if chunks:
                self.cleanup_chunks(chunks)
